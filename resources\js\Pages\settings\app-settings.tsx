import { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { type BreadcrumbItem } from '@/types';
import AppSidebarLayout from '@/layouts/app/app-sidebar-layout';
import SettingsLayout from '@/layouts/settings/layout';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Save, Globe, Clock, Calendar, Database, Download, Plus } from 'lucide-react';
import { Link } from '@inertiajs/react';

interface Currency {
    code: string;
    name: string;
    symbol: string;
}

interface Country {
    iso2: string;
    name: string;
    currency: string;
    currency_symbol: string;
}

interface Timezone {
    value: string;
    label: string;
}

interface Language {
    code: string;
    name: string;
    flag: string;
}

interface DateFormat {
    value: string;
    label: string;
    example: string;
}

interface TimeFormat {
    value: string;
    label: string;
    example: string;
}

interface DatatableRowLimit {
    value: number;
    label: string;
}

interface Company {
    id: number;
    name: string;
    default_currency: string;
    default_country: string;
    date_format: string;
    time_format: string;
    timezone: string;
    default_language: string;
    datatable_row_limit: number;
    employees_can_export_data: boolean;
}

interface Options {
    currencies: Currency[];
    countries: Country[];
    timezones: Timezone[];
    languages: Language[];
    dateFormats: DateFormat[];
    timeFormats: TimeFormat[];
    datatableRowLimits: DatatableRowLimit[];
}

interface Props {
    company: Company;
    options: Options;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'App Settings',
        href: '/settings/app-settings',
    },
];

export default function AppSettings({ company, options }: Props) {
    const { data, setData, put, processing, errors } = useForm({
        default_currency: company.default_currency,
        default_country: company.default_country,
        date_format: company.date_format,
        time_format: company.time_format,
        default_timezone: company.timezone,
        default_language: company.default_language,
        datatable_row_limit: company.datatable_row_limit,
        employees_can_export_data: company.employees_can_export_data,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        put(route('settings.app-settings.update'));
    };

    return (
        <AppSidebarLayout breadcrumbs={breadcrumbs}>
            <Head title="App Settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <HeadingSmall 
                        title="App Settings" 
                        description="Configure default settings for your application" 
                    />
                    
                    <form onSubmit={handleSubmit} className="space-y-6">
                        {/* Date & Time Settings */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Calendar className="h-5 w-5" />
                                    Date & Time Format
                                </CardTitle>
                                <CardDescription>
                                    Configure how dates and times are displayed throughout the application
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="date_format">Date Format</Label>
                                        <Select value={data.date_format} onValueChange={(value) => setData('date_format', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select date format" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.dateFormats.map((format) => (
                                                    <SelectItem key={format.value} value={format.value}>
                                                        <div className="flex items-center justify-between w-full">
                                                            <span>{format.label}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.date_format && <p className="text-sm text-red-600 mt-1">{errors.date_format}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="time_format">Time Format</Label>
                                        <Select value={data.time_format} onValueChange={(value) => setData('time_format', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select time format" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.timeFormats.map((format) => (
                                                    <SelectItem key={format.value} value={format.value}>
                                                        <div className="flex items-center justify-between w-full">
                                                            <span>{format.label}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.time_format && <p className="text-sm text-red-600 mt-1">{errors.time_format}</p>}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Regional Settings */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Globe className="h-5 w-5" />
                                    Regional Settings
                                </CardTitle>
                                <CardDescription>
                                    Set default regional preferences for your application
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="default_currency">Default Currency</Label>
                                        {options.currencies.length === 0 ? (
                                            <div className="mt-1">
                                                <div className="flex items-center justify-between p-3 border border-dashed border-gray-300 rounded-md">
                                                    <div>
                                                        <p className="text-sm text-gray-600">No currencies available</p>
                                                        <p className="text-xs text-gray-500">Add currencies in Currency Settings first</p>
                                                    </div>
                                                    <Link href={route('settings.currency-settings')}>
                                                        <Button size="sm" variant="outline">
                                                            <Plus className="mr-1 h-3 w-3" />
                                                            Add Currency
                                                        </Button>
                                                    </Link>
                                                </div>
                                            </div>
                                        ) : (
                                            <Select value={data.default_currency} onValueChange={(value) => setData('default_currency', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue placeholder="Select currency" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {options.currencies.map((currency) => (
                                                        <SelectItem key={currency.code} value={currency.code}>
                                                            <div className="flex items-center gap-2">
                                                                <span>{currency.symbol}</span>
                                                                <span>{currency.name} ({currency.code})</span>
                                                            </div>
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        )}
                                        {errors.default_currency && <p className="text-sm text-red-600 mt-1">{errors.default_currency}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="default_country">Default Country</Label>
                                        <Select value={data.default_country} onValueChange={(value) => setData('default_country', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select country" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.countries.map((country) => (
                                                    <SelectItem key={country.iso2} value={country.iso2}>
                                                        {country.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.default_country && <p className="text-sm text-red-600 mt-1">{errors.default_country}</p>}
                                    </div>
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="default_timezone">Default Timezone</Label>
                                        <Select value={data.default_timezone} onValueChange={(value) => setData('default_timezone', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select timezone" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.timezones.map((timezone) => (
                                                    <SelectItem key={timezone.value} value={timezone.value}>
                                                        {timezone.label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.default_timezone && <p className="text-sm text-red-600 mt-1">{errors.default_timezone}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="default_language">Language</Label>
                                        <Select value={data.default_language} onValueChange={(value) => setData('default_language', value)}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select language" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {options.languages.map((language) => (
                                                    <SelectItem key={language.code} value={language.code}>
                                                        <div className="flex items-center gap-2">
                                                            <span>{language.flag}</span>
                                                            <span>{language.name}</span>
                                                        </div>
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.default_language && <p className="text-sm text-red-600 mt-1">{errors.default_language}</p>}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Data Management */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Database className="h-5 w-5" />
                                    Data Management
                                </CardTitle>
                                <CardDescription>
                                    Configure data display and export settings
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label htmlFor="datatable_row_limit">Datatable Row Limit</Label>
                                    <Select 
                                        value={data.datatable_row_limit.toString()} 
                                        onValueChange={(value) => setData('datatable_row_limit', parseInt(value))}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select row limit" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {options.datatableRowLimits.map((limit) => (
                                                <SelectItem key={limit.value} value={limit.value.toString()}>
                                                    {limit.label}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.datatable_row_limit && <p className="text-sm text-red-600 mt-1">{errors.datatable_row_limit}</p>}
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Checkbox 
                                        id="employees_can_export_data"
                                        checked={data.employees_can_export_data}
                                        onCheckedChange={(checked) => setData('employees_can_export_data', checked as boolean)}
                                    />
                                    <Label htmlFor="employees_can_export_data" className="flex items-center gap-2">
                                        <Download className="h-4 w-4" />
                                        Employees can export data
                                    </Label>
                                </div>
                                {errors.employees_can_export_data && <p className="text-sm text-red-600 mt-1">{errors.employees_can_export_data}</p>}
                            </CardContent>
                        </Card>

                        <div className="flex justify-end">
                            <Button type="submit" disabled={processing || options.currencies.length === 0}>
                                {processing ? (
                                    <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-300 border-t-white"></div>
                                        Saving...
                                    </>
                                ) : (
                                    <>
                                        <Save className="mr-2 h-4 w-4" />
                                        Save
                                    </>
                                )}
                            </Button>
                        </div>
                    </form>
                </div>
            </SettingsLayout>
        </AppSidebarLayout>
    );
}
