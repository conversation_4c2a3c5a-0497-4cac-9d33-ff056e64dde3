<?php

require_once 'vendor/autoload.php';

use App\Services\GeographicMappingService;

// Bootstrap Laravel
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Geographic Mapping Service Fallback Mechanism\n";
echo "====================================================\n\n";

$geoService = new GeographicMappingService();

// Test case 1: Empty address data (should use fallbacks)
echo "Test 1: Empty address data\n";
$result1 = $geoService->resolveGeographicIds([]);
echo "Result: " . json_encode($result1, JSON_PRETTY_PRINT) . "\n\n";

// Test case 2: Invalid address data (should use fallbacks)
echo "Test 2: Invalid address data\n";
$result2 = $geoService->resolveGeographicIds([
    'country_code' => 'INVALID',
    'country_name' => 'Invalid Country',
    'state' => 'Invalid State',
    'city' => 'Invalid City'
]);
echo "Result: " . json_encode($result2, JSON_PRETTY_PRINT) . "\n\n";

// Test case 3: Partial valid data (should mix resolved and fallback)
echo "Test 3: Partial valid data\n";
$result3 = $geoService->resolveGeographicIds([
    'country_code' => 'TN',
    'country_name' => 'Tunisia',
    'state' => 'Invalid State',
    'city' => 'Invalid City'
]);
echo "Result: " . json_encode($result3, JSON_PRETTY_PRINT) . "\n\n";

echo "Testing completed!\n";
