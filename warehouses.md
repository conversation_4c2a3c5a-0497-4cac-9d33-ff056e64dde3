# Warehouses Module

## Overview
The Warehouses module manages physical storage locations for inventory, including warehouse details, inventory levels, and stock movements.

## Features
- Warehouse management (CRUD operations)
- Inventory tracking by warehouse
- Stock transfers between warehouses
- Warehouse capacity management
- Inventory reports by location

## Database Structure

### Warehouses Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Warehouse name |
| code | string | Warehouse code (unique) |
| address | text | Warehouse address |
| city | string | Warehouse city |
| state | string | Warehouse state/province |
| postal_code | string | Warehouse postal/zip code |
| country | string | Warehouse country |
| contact_name | string | Warehouse manager/contact |
| contact_email | string | Contact email |
| contact_phone | string | Contact phone |
| is_active | boolean | Whether warehouse is active |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Warehouse_Inventory Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| warehouse_id | bigint | Foreign key to warehouses table |
| product_id | bigint | Foreign key to products table |
| quantity | integer | Current quantity in stock |
| min_stock_level | integer | Minimum stock level for alerts |
| max_stock_level | integer | Maximum stock capacity |
| location_code | string | Location within warehouse (aisle/shelf) |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Stock_Movements Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| product_id | bigint | Foreign key to products table |
| from_warehouse_id | bigint | Source warehouse (null for incoming) |
| to_warehouse_id | bigint | Destination warehouse (null for outgoing) |
| quantity | integer | Quantity moved |
| movement_type | string | Type of movement (transfer, adjustment, etc.) |
| reference | string | Reference document (order number, etc.) |
| notes | text | Movement notes |
| performed_by | bigint | User who performed the movement |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

## User Interface
- Warehouse listing page
- Warehouse creation and editing forms
- Warehouse details view with inventory levels
- Stock transfer interface
- Inventory reports by warehouse
- Low stock alerts dashboard

## Business Rules
- Warehouse codes must be unique within a company
- Stock quantities cannot be negative
- Stock transfers require source and destination warehouses
- Inventory adjustments require documentation
- Stock movements create an audit trail
- Order fulfillment may be tied to specific warehouses