import React from "react";
import { <PERSON>, <PERSON> } from "@inertiajs/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from "@/components/ui/card";
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Breadcrumb } from "@/components/ui/breadcrumb";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table";
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Users as UsersIcon, Search } from "lucide-react";

interface Role {
    id: number;
    name: string;
}

interface User {
    id: number;
    name: string;
    email: string;
    role?: Role | null;
    created_at: string;
    last_login_at?: string;
}

interface Props {
    users: {
        data: User[];
        total: number;
        per_page: number;
        current_page: number;
        last_page: number;
    };
    roles: Role[];
    filters: {
        search?: string;
        role?: string;
    };
}

export default function UsersIndex({ users, roles, filters }: Props) {
    const breadcrumbItems = [{ title: "Users" }];

    const handleRoleFilterChange = (value: string) => {
        const url = new URL(window.location.href);
        if (value !== "all") {
            url.searchParams.set('role', value);
        } else {
            url.searchParams.delete('role');
        }
        window.location.href = url.toString();
    };

    const handleSearch = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault();
        const formData = new FormData(e.currentTarget);
        const searchValue = formData.get('search') as string;
        
        const url = new URL(window.location.href);
        if (searchValue) {
            url.searchParams.set('search', searchValue);
        } else {
            url.searchParams.delete('search');
        }
        window.location.href = url.toString();
    };

    return (
        <AppSidebarLayout>
            <Head title="Users" />
            <div className="p-6 space-y-6">
                <Breadcrumb items={breadcrumbItems} className="mb-6" />

                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">Users</h2>
                        <p className="text-muted-foreground">
                            Manage company users and their roles
                        </p>
                    </div>

                    <Button asChild>
                        <Link href={route("users.create")}>Add User</Link>
                    </Button>
                </div>

                <div className="flex items-start space-x-4">
                    {/* Stats Card */}
                    <Card className="flex-[2]">
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                Total Users
                            </CardTitle>
                            <UsersIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{users.total}</div>
                            <p className="text-xs text-muted-foreground">
                                Active company users
                            </p>
                        </CardContent>
                    </Card>

                    {/* Filters */}
                    <Card className="flex-[8]">
                        <CardHeader>
                            <CardTitle>Filters</CardTitle>
                            <CardDescription>
                                Filter and search through users
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <form onSubmit={handleSearch} className="space-y-4">
                                <div className="relative">
                                    <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Search users..."
                                        className="pl-8"
                                        name="search"
                                        defaultValue={filters.search}
                                    />
                                </div>
                                <Select
                                    defaultValue={filters.role || "all"}
                                    onValueChange={handleRoleFilterChange}
                                >
                                    <SelectTrigger className="w-full">
                                        <SelectValue placeholder="Filter by role" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Roles</SelectItem>
                                        {roles.map((role) => (
                                            <SelectItem
                                                key={role.id}
                                                value={role.id.toString()}
                                            >
                                                {role.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </form>
                        </CardContent>
                    </Card>
                </div>

                {/* Users Table */}
                <Card>
                    <CardHeader>
                        <CardTitle>Users List</CardTitle>
                        <CardDescription>
                            A list of all users in your company
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Name</TableHead>
                                    <TableHead>Email</TableHead>
                                    <TableHead>Role</TableHead>
                                    <TableHead>Joined</TableHead>
                                    <TableHead>Last Active</TableHead>
                                    <TableHead className="text-right">Actions</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {users.data.map((user) => (
                                    <TableRow key={user.id}>
                                        <TableCell className="font-medium">
                                            {user.name}
                                        </TableCell>
                                        <TableCell>{user.email}</TableCell>
                                        <TableCell>
                                            <Badge variant="secondary">
                                                {user.role?.name || 'No Role'}
                                            </Badge>
                                        </TableCell>
                                        <TableCell>
                                            {new Date(user.created_at).toLocaleDateString()}
                                        </TableCell>
                                        <TableCell>
                                            {user.last_login_at
                                                ? new Date(user.last_login_at).toLocaleDateString()
                                                : "Never"}
                                        </TableCell>
                                        <TableCell className="text-right">
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                asChild
                                            >
                                                <Link
                                                    href={route("users.edit", user.id)}
                                                >
                                                    Edit
                                                </Link>
                                            </Button>
                                        </TableCell>
                                    </TableRow>
                                ))}
                            </TableBody>
                        </Table>

                        {/* Pagination */}
                        {users.last_page > 1 && (
                            <div className="flex items-center justify-between px-2 py-4">
                                <div className="text-sm text-muted-foreground">
                                    Showing {users.current_page} of {users.last_page} pages
                                </div>
                                <div className="space-x-2">
                                    {users.current_page > 1 && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            asChild
                                        >
                                            <Link
                                                href={route("users.index", {
                                                    page: users.current_page - 1,
                                                    ...filters,
                                                })}
                                            >
                                                Previous
                                            </Link>
                                        </Button>
                                    )}
                                    {users.current_page < users.last_page && (
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            asChild
                                        >
                                            <Link
                                                href={route("users.index", {
                                                    page: users.current_page + 1,
                                                    ...filters,
                                                })}
                                            >
                                                Next
                                            </Link>
                                        </Button>
                                    )}
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppSidebarLayout>
    );
} 