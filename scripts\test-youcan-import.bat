@echo off
REM YouCan Import Testing Script for Windows
REM This script helps you quickly test YouCan webhook and order import functionality

REM Change to project root directory (one level up from scripts folder)
cd /d "%~dp0.."

echo.
echo 🚀 YouCan Import Testing Script
echo ================================
echo Current directory: %CD%
echo.

:menu
echo Choose an option:
echo 1) Check Laravel ^& Queue status
echo 2) Test YouCan API connection
echo 3) Import 200 orders from YouCan
echo 4) Check import progress
echo 5) Setup webhook testing
echo 6) Monitor logs
echo 7) Run all tests
echo 0) Exit
echo.
set /p choice="Enter your choice [0-7]: "

if "%choice%"=="1" goto check_status
if "%choice%"=="2" goto test_connection
if "%choice%"=="3" goto import_orders
if "%choice%"=="4" goto check_progress
if "%choice%"=="5" goto setup_webhook
if "%choice%"=="6" goto monitor_logs
if "%choice%"=="7" goto run_all_tests
if "%choice%"=="0" goto exit
echo Invalid option. Please choose 0-7.
goto menu

:check_status
echo.
echo ℹ️  Checking if <PERSON><PERSON> is running...
curl -s http://localhost:8000 >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Laravel is running on port 8000
) else (
    echo ❌ Laravel is not running. Please start with: php artisan serve
)
echo.
echo ℹ️  Queue system check...
php artisan queue:work --help >nul 2>&1
if %errorlevel%==0 (
    echo ✅ Queue system is available
    echo ⚠️  Make sure to run: php artisan queue:work in a separate terminal
) else (
    echo ❌ Queue system not available
)
goto continue

:test_connection
echo.
echo ℹ️  Testing YouCan API connection...
php artisan youcan:test-import --company-id=1 --test-connection
goto continue

:import_orders
echo.
echo ℹ️  Starting import of 200 orders from YouCan...
echo This will dispatch a background job to import orders.
echo Monitor the queue worker terminal for progress.
echo.
php -r "require 'vendor/autoload.php'; $app = require_once 'bootstrap/app.php'; $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); App\Jobs\SyncYoucanOrders::dispatch(1, ['limit' => 200, 'page' => 1]); echo '✅ Job dispatched successfully!' . PHP_EOL; echo 'Monitor progress with option 6 (Monitor logs)' . PHP_EOL;"
goto continue

:check_progress
echo.
echo ℹ️  Checking import progress...
php -r "require 'vendor/autoload.php'; $app = require_once 'bootstrap/app.php'; $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); $count = App\Models\Order::where('source', 'youcan')->count(); $customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count(); $latest = App\Models\Order::where('source', 'youcan')->latest()->first(); echo '📊 Import Statistics:' . PHP_EOL; echo '  - Orders imported: ' . $count . PHP_EOL; echo '  - Customers created: ' . $customerCount . PHP_EOL; echo '  - Latest order: ' . ($latest ? $latest->ref . ' (' . $latest->created_at . ')' : 'None') . PHP_EOL;"
goto continue

:setup_webhook
echo.
echo ℹ️  Setting up webhook testing...
echo Make sure ngrok is running with: ngrok http 8000
echo Then update your .env file with the ngrok HTTPS URL
echo.
echo To subscribe to webhooks, visit your app and go to:
echo YouCan Import page → Subscribe to Webhooks
echo.
echo Or test webhook endpoint manually:
echo curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \
echo      -H "Content-Type: application/json" \
echo      -d "{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}"
goto continue

:monitor_logs
echo.
echo ℹ️  Starting log monitoring...
echo Press Ctrl+C to stop monitoring
echo.
powershell -Command "Get-Content storage/logs/laravel.log -Wait | Select-String -Pattern 'youcan|order|webhook' -CaseSensitive:$false"
goto continue

:run_all_tests
echo.
echo ℹ️  Running all tests...
call :check_status
call :test_connection
call :import_orders
echo.
echo ✅ All tests initiated!
echo ℹ️  Monitor progress with option 6 (Monitor logs)
echo ℹ️  Check results with option 4 (Check import progress)
goto continue

:continue
echo.
pause
goto menu

:exit
echo.
echo ✅ Goodbye!
pause
exit /b 0
