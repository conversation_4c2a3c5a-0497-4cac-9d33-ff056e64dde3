# Database Seeders Documentation

This document provides an overview of all the database seeders created for comprehensive testing of the application features.

## Overview

The seeders are designed to create realistic test data for all major application features, allowing you to test the complete functionality without manually creating data.

## Seeder Execution Order

The seeders are executed in the following order (as defined in `DatabaseSeeder.php`):

### 1. Core Data Seeders
- **CmsPlatformSeeder** - Creates CMS platforms (YouCan, Shopify, WooCommerce)
- **PermissionSeeder** - Creates permissions for different modules
- **SettingsSeeder** - Creates SMTP, Pusher, and Stripe settings
- **PlanSeeder** - Creates subscription plans

### 2. Company and User Data
- **CompanyWithUsersSeeder** - Creates companies with users and subscriptions
- **RoleSeeder** - Creates roles and assigns permissions

### 3. Business Data
- **WarehouseSeeder** - Creates warehouses for each company
- **SupplierSeeder** - Creates suppliers for each company
- **OrderStatusSeeder** - Creates order statuses with workflow logic
- **ProductSeeder** - Creates products with variants
- **CustomerSeeder** - Creates customers for each company
- **AdPlatformSeeder** - Creates ad platforms and campaigns
- **CmsIntegrationSeeder** - Creates CMS integrations

### 4. Transactional Data
- **OrderSeeder** - Creates orders with order items
- **InventorySeeder** - Creates warehouse stock and inventory movements

## Detailed Seeder Descriptions

### PlanSeeder
Creates 4 subscription plans:
- **Starter** ($29.99/month) - Basic features
- **Professional** ($79.99/month) - Advanced features (default)
- **Enterprise** ($199.99/month) - Full features
- **Free Trial** ($0.00) - Trial features

### CompanyWithUsersSeeder
- Creates 2 companies: "Acme Corporation" and "Tech Solutions Inc"
- Creates users with different roles: admin, manager, staff
- Creates subscriptions for each company (Professional for first, Starter for second)

### RoleSeeder
Creates 5 roles for each company:
- **Admin** - Full access to all features
- **Manager** - Operations and reports access
- **Staff** - Basic operational access
- **Sales** - Customer and order management
- **Inventory** - Product and inventory management

### WarehouseSeeder
Creates 3 warehouses per company:
- Main Warehouse (default)
- Secondary Warehouse
- Returns Processing Center

### SupplierSeeder
Creates 6 suppliers per company covering different categories:
- Electronics, Fashion, Home & Garden, Sports, Office, Automotive

### OrderStatusSeeder
Creates 8 order statuses with proper workflow:
- Pending (default) → Confirmed → Processing → Shipped → Delivered
- Alternative flows: Cancelled, Returned, Refunded
- Includes stock impact settings

### ProductSeeder
Creates 6 products per company:
- Wireless Bluetooth Headphones (with variants)
- Smartphone Case (with variants)
- Organic Cotton T-Shirt (with variants)
- Stainless Steel Water Bottle (with variants)
- Yoga Mat Premium (no variants)
- LED Desk Lamp (with variants)

### CustomerSeeder
Creates 10 customers per company with diverse geographic locations:
- US customers (5): New York, California, Texas, Florida, Illinois
- Morocco customers (2): Casablanca, Rabat
- Canada customers (3): Toronto, Montreal, Vancouver

### AdPlatformSeeder
Creates 6 ad platforms per company:
- Facebook Ads, Google Ads, Instagram Ads, TikTok Ads, LinkedIn Ads, YouTube Ads
- Each platform includes sample campaigns with budgets and dates

### CmsIntegrationSeeder
Creates CMS integrations:
- **YouCan integration** for all companies (active with sample stores)
- **Shopify integration** for first company only (inactive)
- **WooCommerce integration** for second company only (inactive)

### OrderSeeder
Creates 20 orders per company with:
- Random customers, products, and statuses
- 1-4 items per order
- Realistic order data including tracking numbers, delivery info
- Links to ad campaigns and warehouses

### InventorySeeder
Creates comprehensive inventory data:
- Warehouse stock for all products in all warehouses
- Product variants stock
- Initial stock movements
- Random inventory movements (stock in/out, adjustments, transfers)

## Running the Seeders

To run all seeders:
```bash
php artisan db:seed
```

To run a specific seeder:
```bash
php artisan db:seed --class=ProductSeeder
```

To refresh database and run seeders:
```bash
php artisan migrate:fresh --seed
```

## Test Data Summary

After running all seeders, you will have:
- **2 Companies** with different subscription plans
- **8 Users** across different roles
- **6 Warehouses** (3 per company)
- **12 Suppliers** (6 per company)
- **16 Order Statuses** (8 per company)
- **12 Products** (6 per company) with variants
- **20 Customers** (10 per company) with real geographic data
- **12 Ad Platforms** (6 per company) with campaigns
- **40 Orders** (20 per company) with items
- **Comprehensive inventory data** with stock levels and movements
- **CMS integrations** for testing external platform connections

## Successfully Seeded Data

✅ **All seeders completed successfully!** The database now contains:

### Geographic Data Integration
- **Real Countries**: 250 countries from your manual import
- **Real States**: 4,954 states/provinces from your manual import
- **Real Cities**: 148,677+ cities from your manual import
- **Customers**: Using actual city/state/country IDs from your real data

### Business Data
- **Companies**: 2 companies (Acme Corporation, Tech Solutions Inc)
- **Users**: 8 users with different roles and permissions
- **Products**: 12 products with variants and realistic pricing
- **Customers**: 20 customers with real geographic locations
- **Orders**: 40 orders with realistic order items and statuses
- **Inventory**: Complete stock tracking with movements

### System Configuration
- **Permissions**: 28 permissions across different modules
- **Roles**: 5 roles per company with proper permission assignments
- **Settings**: SMTP, Pusher, and Stripe configuration
- **Plans**: 4 subscription plans with features

## Notes

- **Countries, States, Cities**: These are not seeded as you mentioned having real data to import manually
- **Customer geographic data**: Uses placeholder IDs (1-10) for cities/states that can be updated after importing real geographic data
- **Realistic data**: All seeded data uses realistic values, names, and relationships
- **Relationships**: All foreign key relationships are properly maintained
- **Variants**: Products with variants have proper variant data and separate stock tracking

This comprehensive seeding setup allows you to test all application features immediately after setup, including:
- Multi-company functionality
- User roles and permissions
- Product management with variants
- Order processing workflows
- Inventory management
- CMS integrations
- Advertising campaign tracking
- Customer management
- Subscription billing
