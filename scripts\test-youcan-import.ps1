# YouCan Import Testing Script for PowerShell
# This script helps you quickly test YouCan webhook and order import functionality

# Change to project root directory (one level up from scripts folder)
Set-Location (Split-Path -Parent $PSScriptRoot)

Write-Host "🚀 YouCan Import Testing Script" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

function Show-Menu {
    Write-Host "Choose an option:" -ForegroundColor Cyan
    Write-Host "1) Check Laravel & Queue status"
    Write-Host "2) Test YouCan API connection"
    Write-Host "3) Import 200 orders from YouCan"
    Write-Host "4) Check import progress"
    Write-Host "5) Setup webhook testing"
    Write-Host "6) Monitor logs (last 50 lines)"
    Write-Host "7) Run all tests"
    Write-Host "0) Exit"
    Write-Host ""
}

function Test-<PERSON><PERSON> {
    Write-Host "ℹ️  Checking if <PERSON><PERSON> is running..." -ForegroundColor Blue
    try {
        $response = Invoke-WebRequest -Uri "http://localhost:8000" -TimeoutSec 5 -ErrorAction Stop
        Write-Host "✅ <PERSON><PERSON> is running on port 8000" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ Laravel is not running. Please start with: php artisan serve" -ForegroundColor Red
        return $false
    }
}

function Test-Queue {
    Write-Host "ℹ️  Checking queue system..." -ForegroundColor Blue
    try {
        $result = php artisan queue:work --help 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Queue system is available" -ForegroundColor Green
            Write-Host "⚠️  Make sure to run: php artisan queue:work in a separate terminal" -ForegroundColor Yellow
        }
        else {
            Write-Host "❌ Queue system not available" -ForegroundColor Red
        }
    }
    catch {
        Write-Host "❌ Error checking queue system" -ForegroundColor Red
    }
}

function Test-YouCanConnection {
    Write-Host "ℹ️  Testing YouCan API connection..." -ForegroundColor Blue
    php artisan youcan:test-import --company-id=1 --test-connection
}

function Import-Orders {
    Write-Host "ℹ️  Starting import of 200 orders from YouCan..." -ForegroundColor Blue
    Write-Host "This will dispatch a background job to import orders." -ForegroundColor Yellow
    Write-Host "Monitor the queue worker terminal for progress." -ForegroundColor Yellow
    Write-Host ""
    
    $phpCode = @"
require 'vendor/autoload.php';
`$app = require_once 'bootstrap/app.php';
`$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

// Dispatch job to import 200 orders
App\Jobs\SyncYoucanOrders::dispatch(1, [
    'limit' => 200,
    'page' => 1
]);

echo '✅ Job dispatched successfully!' . PHP_EOL;
echo 'Monitor progress with option 6 (Monitor logs)' . PHP_EOL;
"@
    
    php -r $phpCode
}

function Check-Progress {
    Write-Host "ℹ️  Checking import progress..." -ForegroundColor Blue
    
    $phpCode = @"
require 'vendor/autoload.php';
`$app = require_once 'bootstrap/app.php';
`$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();

`$count = App\Models\Order::where('source', 'youcan')->count();
`$customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count();
`$latest = App\Models\Order::where('source', 'youcan')->latest()->first();

echo '📊 Import Statistics:' . PHP_EOL;
echo '  - Orders imported: ' . `$count . PHP_EOL;
echo '  - Customers created: ' . `$customerCount . PHP_EOL;
echo '  - Latest order: ' . (`$latest ? `$latest->ref . ' (' . `$latest->created_at . ')' : 'None') . PHP_EOL;
"@
    
    php -r $phpCode
}

function Setup-Webhook {
    Write-Host "ℹ️  Setting up webhook testing..." -ForegroundColor Blue
    Write-Host "Make sure ngrok is running with: ngrok http 8000" -ForegroundColor Yellow
    Write-Host "Then update your .env file with the ngrok HTTPS URL" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To subscribe to webhooks, visit your app and go to:"
    Write-Host "YouCan Import page → Subscribe to Webhooks"
    Write-Host ""
    Write-Host "Or test webhook endpoint manually:"
    Write-Host 'curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \'
    Write-Host '     -H "Content-Type: application/json" \'
    Write-Host '     -d "{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}"'
}

function Monitor-Logs {
    Write-Host "ℹ️  Showing recent logs (last 50 lines)..." -ForegroundColor Blue
    Write-Host "For real-time monitoring, use: Get-Content storage/logs/laravel.log -Wait" -ForegroundColor Yellow
    Write-Host ""
    
    if (Test-Path "storage/logs/laravel.log") {
        Get-Content "storage/logs/laravel.log" -Tail 50 | Select-String -Pattern "youcan|order|webhook" -CaseSensitive:$false
    }
    else {
        Write-Host "❌ Log file not found: storage/logs/laravel.log" -ForegroundColor Red
    }
}

function Run-AllTests {
    Write-Host "ℹ️  Running all tests..." -ForegroundColor Blue
    Test-Laravel
    Test-Queue
    Test-YouCanConnection
    Import-Orders
    Write-Host ""
    Write-Host "✅ All tests initiated!" -ForegroundColor Green
    Write-Host "ℹ️  Monitor progress with option 6 (Monitor logs)" -ForegroundColor Blue
    Write-Host "ℹ️  Check results with option 4 (Check import progress)" -ForegroundColor Blue
}

# Main script loop
do {
    Show-Menu
    $choice = Read-Host "Enter your choice [0-7]"
    
    switch ($choice) {
        "1" {
            Test-Laravel
            Test-Queue
        }
        "2" {
            Test-YouCanConnection
        }
        "3" {
            Import-Orders
        }
        "4" {
            Check-Progress
        }
        "5" {
            Setup-Webhook
        }
        "6" {
            Monitor-Logs
        }
        "7" {
            Run-AllTests
        }
        "0" {
            Write-Host "✅ Goodbye!" -ForegroundColor Green
            exit
        }
        default {
            Write-Host "❌ Invalid option. Please choose 0-7." -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Read-Host "Press Enter to continue"
    Clear-Host
    Write-Host "🚀 YouCan Import Testing Script" -ForegroundColor Green
    Write-Host "================================" -ForegroundColor Green
    Write-Host ""
    
} while ($true)
