<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Company;
use App\Models\CmsPlatform;
use App\Models\CompanyCmsIntegration;
use App\Models\CompanyCmsIntegrationStore;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Queue;
use App\Jobs\ProcessYoucanWebhook;

class YoucanWebhookTest extends TestCase
{
    // Don't use RefreshDatabase to avoid migration issues
    // use RefreshDatabase;

    protected $user;
    protected $company;
    protected $platform;
    protected $integration;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->company = Company::factory()->create();
        $this->user = User::factory()->create(['company_id' => $this->company->id]);
        
        $this->platform = CmsPlatform::create([
            'name' => 'YouCan',
            'slug' => 'youcan',
            'api_base_url' => 'https://api.youcan.shop',
            'is_active' => true,
        ]);

        $this->integration = CompanyCmsIntegration::create([
            'company_id' => $this->company->id,
            'cms_platform_id' => $this->platform->id,
            'is_active' => true,
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'password',
                'client_secret' => 'test-secret',
            ],
        ]);

        // Create a store for this integration
        CompanyCmsIntegrationStore::create([
            'company_cms_integration_id' => $this->integration->id,
            'store_id' => 'test-store-123',
            'store_name' => 'Test Store',
            'is_default' => true,
        ]);
    }

    /** @test */
    public function it_can_handle_valid_webhook_with_signature()
    {
        Queue::fake();

        $payload = [
            'event' => 'order.create',
            'store_id' => 'test-store-123',
            'data' => [
                'id' => 'order-123',
                'store_id' => 'test-store-123',
                'customer' => [
                    'id' => 'customer-456',
                    'email' => '<EMAIL>',
                    'name' => 'John Doe',
                ],
                'total' => 100.00,
                'status' => 'pending',
            ],
        ];

        // Generate valid signature
        $signature = hash_hmac('sha256', json_encode($payload), 'test-secret');

        $response = $this->postJson('/api/youcan/webhook', $payload, [
            'x-youcan-signature' => $signature,
        ]);

        $response->assertStatus(200);
        $response->assertJson(['status' => 'success']);

        // Verify job was dispatched
        Queue::assertPushed(ProcessYoucanWebhook::class, function ($job) {
            return $job->companyId === $this->company->id;
        });
    }

    /** @test */
    public function it_rejects_webhook_with_invalid_signature()
    {
        Queue::fake();

        $payload = [
            'event' => 'order.create',
            'store_id' => 'test-store-123',
            'data' => ['id' => 'order-123'],
        ];

        $response = $this->postJson('/api/youcan/webhook', $payload, [
            'x-youcan-signature' => 'invalid-signature',
        ]);

        $response->assertStatus(401);
        $response->assertJson(['error' => 'Invalid signature']);

        // Verify no job was dispatched
        Queue::assertNotPushed(ProcessYoucanWebhook::class);
    }

    /** @test */
    public function it_can_identify_company_by_store_id()
    {
        Queue::fake();

        $payload = [
            'event' => 'order.create',
            'store_id' => 'test-store-123',
            'data' => ['id' => 'order-123'],
        ];

        $signature = hash_hmac('sha256', json_encode($payload), 'test-secret');

        $response = $this->postJson('/api/youcan/webhook', $payload, [
            'x-youcan-signature' => $signature,
        ]);

        $response->assertStatus(200);

        // Verify correct company was identified
        Queue::assertPushed(ProcessYoucanWebhook::class, function ($job) {
            return $job->companyId === $this->company->id;
        });
    }

    /** @test */
    public function it_rejects_webhook_for_unknown_store()
    {
        Queue::fake();

        $payload = [
            'event' => 'order.create',
            'store_id' => 'unknown-store-456',
            'data' => ['id' => 'order-123'],
        ];

        $signature = hash_hmac('sha256', json_encode($payload), 'test-secret');

        $response = $this->postJson('/api/youcan/webhook', $payload, [
            'x-youcan-signature' => $signature,
        ]);

        $response->assertStatus(400);
        $response->assertJson(['error' => 'Could not determine company']);

        // Verify no job was dispatched
        Queue::assertNotPushed(ProcessYoucanWebhook::class);
    }

    /** @test */
    public function it_allows_webhook_without_signature_when_no_secret_configured()
    {
        Queue::fake();

        // Update integration to have no client_secret
        $this->integration->update([
            'credentials' => [
                'email' => '<EMAIL>',
                'password' => 'password',
            ],
        ]);

        $payload = [
            'event' => 'order.create',
            'store_id' => 'test-store-123',
            'data' => ['id' => 'order-123'],
        ];

        $response = $this->postJson('/api/youcan/webhook', $payload);

        $response->assertStatus(200);
        $response->assertJson(['status' => 'success']);

        // Verify job was dispatched
        Queue::assertPushed(ProcessYoucanWebhook::class);
    }
}
