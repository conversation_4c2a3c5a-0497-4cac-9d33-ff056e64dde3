import { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Trash2, MoreHorizontal } from 'lucide-react';
import { Checkbox } from '@/components/ui/checkbox';
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface Supplier {
    id: number;
    name: string;
    code: string;
    contact_person: string | null;
    phone: string | null;
    email: string | null;
    address: string | null;
    status: 'active' | 'inactive';
}

interface PaginatedData<T> {
    data: T[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
}

interface Props {
    suppliers: PaginatedData<Supplier>;
    filters: {
        search?: string;
        status?: string;
        sort?: string;
        direction?: 'asc' | 'desc';
        per_page?: number;
    };
}

export default function Index({ suppliers, filters }: Props) {
    const [search, setSearch] = useState(filters.search || '');
    const [status, setStatus] = useState(filters.status || 'all');
    const [perPage, setPerPage] = useState(filters.per_page?.toString() || '10');
    const [selectedSuppliers, setSelectedSuppliers] = useState<number[]>([]);
    const [isSelectAll, setIsSelectAll] = useState(false);
    const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
    const [openAlertDialog, setOpenAlertDialog] = useState<number | null>(null);

    const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        setSearch(value);
        
        // Debounce the search to avoid too many requests
        const timeoutId = setTimeout(() => {
            router.get(route('suppliers.index'), { 
                ...filters,
                search: value || undefined 
            }, {
                preserveState: true,
                preserveScroll: true,
            });
        }, 300);

        return () => clearTimeout(timeoutId);
    };

    const handleFilterChange = (key: string, value: string | undefined) => {
        router.get(route('suppliers.index'), {
            ...filters,
            [key]: value || undefined,
            page: 1, // Reset to first page when changing filters
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSort = (column: string) => {
        const newDirection = filters.sort === column && filters.direction === 'asc' ? 'desc' : 'asc';
        router.get(route('suppliers.index'), {
            ...filters,
            sort: column,
            direction: newDirection,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSelectAll = (checked: boolean) => {
        setIsSelectAll(checked);
        setSelectedSuppliers(checked ? suppliers.data.map(s => s.id) : []);
    };

    const handleSelectSupplier = (supplierId: number, checked: boolean) => {
        setSelectedSuppliers(prev => 
            checked 
                ? [...prev, supplierId]
                : prev.filter(id => id !== supplierId)
        );
    };

    const handleDelete = (supplierId: number) => {
        router.delete(route('suppliers.destroy', supplierId), {
            preserveScroll: true,
            onSuccess: () => {
                setOpenAlertDialog(null);
                setSelectedSuppliers(prev => prev.filter(id => id !== supplierId));
                router.visit(route('suppliers.index'), {
                    preserveState: true,
                    preserveScroll: true,
                    only: ['suppliers'],
                });
            },
        });
    };

    const handleBulkDelete = () => {
        router.delete(route('suppliers.destroy', { suppliers: selectedSuppliers }), {
            preserveScroll: true,
            onSuccess: () => {
                setIsDeleteDialogOpen(false);
                setSelectedSuppliers([]);
                setIsSelectAll(false);
                router.visit(route('suppliers.index'), {
                    preserveState: true,
                    preserveScroll: true,
                    only: ['suppliers'],
                });
            },
        });
    };

    const handleClearFilters = () => {
        router.get(route('suppliers.index'), {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handlePerPageChange = (value: string) => {
        router.get(route('suppliers.index'), {
            ...filters,
            per_page: value,
            page: 1,
        }, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const SortableHeader = ({ column, label }: { column: string; label: string }) => (
        <TableHead className="cursor-pointer" onClick={() => handleSort(column)}>
            <div className="flex items-center">
                {label}
                {filters.sort === column && (
                    <span className="ml-1 text-xs">
                        {filters.direction === 'asc' ? '↑' : '↓'}
                    </span>
                )}
            </div>
        </TableHead>
    );

    return (
        <AppSidebarLayout>
            <Head title="Suppliers" />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <h1 className="text-2xl font-semibold">Suppliers</h1>
                    <div className="flex items-center space-x-2">
                        <Button 
                            size="sm"
                            asChild
                        >
                            <Link href={route('suppliers.create')}>
                                <Plus className="h-4 w-4 mr-2" />
                                Add Supplier
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Filters */}
                <div className="space-y-4 mb-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="relative">
                            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                            <Input
                                placeholder="Search suppliers..."
                                value={search}
                                onChange={handleSearch}
                                className="pl-8"
                            />
                        </div>
                        <select 
                            value={status} 
                            onChange={(e) => handleFilterChange('status', e.target.value === "all" ? undefined : e.target.value)}
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                            <option value="all">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                        <Button 
                            variant="outline" 
                            onClick={handleClearFilters}
                            className="w-full"
                        >
                            Clear Filters
                        </Button>
                    </div>
                </div>

                {/* Bulk Actions */}
                {selectedSuppliers.length > 0 && (
                    <div className="flex items-center space-x-2 mb-4">
                        <span className="text-sm text-muted-foreground">
                            {selectedSuppliers.length} selected
                        </span>
                        <AlertDialog>
                            <AlertDialogTrigger asChild>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-destructive"
                                >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete Selected
                                </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                                <AlertDialogHeader>
                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                        This action cannot be undone. This will permanently delete {selectedSuppliers.length} selected supplier{selectedSuppliers.length === 1 ? '' : 's'} and remove their data from the system.
                                    </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction onClick={handleBulkDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                                        Delete
                                    </AlertDialogAction>
                                </AlertDialogFooter>
                            </AlertDialogContent>
                        </AlertDialog>
                    </div>
                )}

                <div className="rounded-md border">
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead className="w-[50px]">
                                    <Checkbox
                                        checked={isSelectAll}
                                        onCheckedChange={handleSelectAll}
                                    />
                                </TableHead>
                                <SortableHeader column="name" label="Name" />
                                <SortableHeader column="code" label="Code" />
                                <SortableHeader column="contact_person" label="Contact Person" />
                                <SortableHeader column="phone" label="Phone" />
                                <SortableHeader column="email" label="Email" />
                                <SortableHeader column="status" label="Status" />
                                <TableHead className="w-[50px]"></TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {suppliers.data.map((supplier) => (
                                <TableRow key={supplier.id}>
                                    <TableCell>
                                        <Checkbox
                                            checked={selectedSuppliers.includes(supplier.id)}
                                            onCheckedChange={(checked) => 
                                                handleSelectSupplier(supplier.id, checked as boolean)
                                            }
                                        />
                                    </TableCell>
                                    <TableCell>{supplier.name}</TableCell>
                                    <TableCell>{supplier.code}</TableCell>
                                    <TableCell>{supplier.contact_person || '-'}</TableCell>
                                    <TableCell>{supplier.phone || '-'}</TableCell>
                                    <TableCell>{supplier.email || '-'}</TableCell>
                                    <TableCell>
                                        <Badge
                                            variant={supplier.status === 'active' ? 'default' : 'secondary'}
                                        >
                                            {supplier.status}
                                        </Badge>
                                    </TableCell>
                                    <TableCell>
                                        <DropdownMenu modal={false}>
                                            <DropdownMenuTrigger asChild>
                                                <Button variant="ghost" className="h-8 w-8 p-0">
                                                    <span className="sr-only">Open menu</span>
                                                    <MoreHorizontal className="h-4 w-4" />
                                                </Button>
                                            </DropdownMenuTrigger>
                                            <DropdownMenuContent align="end">
                                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                                <DropdownMenuItem asChild>
                                                    <Link href={route('suppliers.show', supplier.id)}>
                                                        View Details
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuItem asChild>
                                                    <Link href={route('suppliers.edit', supplier.id)}>
                                                        Edit
                                                    </Link>
                                                </DropdownMenuItem>
                                                <DropdownMenuSeparator />
                                                <DropdownMenuItem 
                                                    className="text-destructive"
                                                    onSelect={(e) => {
                                                        e.preventDefault();
                                                        setOpenAlertDialog(supplier.id);
                                                    }}
                                                >
                                                    Delete
                                                </DropdownMenuItem>
                                            </DropdownMenuContent>
                                        </DropdownMenu>
                                    </TableCell>
                                </TableRow>
                            ))}
                            {suppliers.data.length === 0 && (
                                <TableRow>
                                    <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                                        No suppliers found
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>

                    {suppliers.last_page > 1 && (
                        <div className="flex items-center justify-between px-4 py-3 border-t">
                            <div className="flex items-center gap-4">
                                <div className="text-sm text-muted-foreground">
                                    Showing {suppliers.from} to {suppliers.to} of {suppliers.total} suppliers
                                </div>
                                <select
                                    value={perPage}
                                    onChange={(e) => handlePerPageChange(e.target.value)}
                                    className="flex h-10 w-[120px] rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                >
                                    <option value="10">10 per page</option>
                                    <option value="50">50 per page</option>
                                    <option value="100">100 per page</option>
                                </select>
                            </div>
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        router.get(route('suppliers.index'), {
                                            ...filters,
                                            page: 1,
                                        }, {
                                            preserveState: true,
                                            preserveScroll: true,
                                        });
                                    }}
                                    disabled={suppliers.current_page === 1}
                                >
                                    First
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        router.get(route('suppliers.index'), {
                                            ...filters,
                                            page: suppliers.current_page - 1,
                                        }, {
                                            preserveState: true,
                                            preserveScroll: true,
                                        });
                                    }}
                                    disabled={suppliers.current_page === 1}
                                >
                                    Previous
                                </Button>
                                <span className="text-sm text-muted-foreground px-2">
                                    Page {suppliers.current_page} of {suppliers.last_page}
                                </span>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        router.get(route('suppliers.index'), {
                                            ...filters,
                                            page: suppliers.current_page + 1,
                                        }, {
                                            preserveState: true,
                                            preserveScroll: true,
                                        });
                                    }}
                                    disabled={suppliers.current_page === suppliers.last_page}
                                >
                                    Next
                                </Button>
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => {
                                        router.get(route('suppliers.index'), {
                                            ...filters,
                                            page: suppliers.last_page,
                                        }, {
                                            preserveState: true,
                                            preserveScroll: true,
                                        });
                                    }}
                                    disabled={suppliers.current_page === suppliers.last_page}
                                >
                                    Last
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {suppliers.data.map((supplier) => (
                <AlertDialog 
                    key={`delete-dialog-${supplier.id}`}
                    open={openAlertDialog === supplier.id} 
                    onOpenChange={(open) => {
                        if (!open) {
                            setOpenAlertDialog(null);
                            document.body.style.removeProperty('pointer-events');
                        }
                    }}
                >
                    <AlertDialogContent>
                        <AlertDialogHeader>
                            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
                            <AlertDialogDescription>
                                This action cannot be undone. This will permanently delete the supplier
                                and remove its data from our servers.
                            </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                            <AlertDialogCancel onClick={() => setOpenAlertDialog(null)}>
                                Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction
                                onClick={() => handleDelete(supplier.id)}
                                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                            >
                                Delete
                            </AlertDialogAction>
                        </AlertDialogFooter>
                    </AlertDialogContent>
                </AlertDialog>
            ))}
        </AppSidebarLayout>
    );
} 