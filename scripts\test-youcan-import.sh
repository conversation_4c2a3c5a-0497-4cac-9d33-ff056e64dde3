#!/bin/bash

# YouCan Import Testing Script
# This script helps you quickly test YouCan webhook and order import functionality

echo "🚀 YouCan Import Testing Script"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if <PERSON><PERSON> is running
check_laravel() {
    print_info "Checking if <PERSON><PERSON> is running..."
    if curl -s http://localhost:8000 > /dev/null; then
        print_status "<PERSON><PERSON> is running on port 8000"
    else
        print_error "<PERSON><PERSON> is not running. Please start with: php artisan serve"
        exit 1
    fi
}

# Check if queue worker is running
check_queue() {
    print_info "Checking queue configuration..."
    if php artisan queue:work --help > /dev/null 2>&1; then
        print_status "Queue system is available"
        print_warning "Make sure to run: php artisan queue:work in a separate terminal"
    else
        print_error "Queue system not available"
    fi
}

# Test YouCan connection
test_connection() {
    print_info "Testing YouCan API connection..."
    php artisan youcan:test-import --company-id=1 --test-connection
}

# Import 200 orders
import_orders() {
    print_info "Starting import of 200 orders from YouCan..."
    echo "This will dispatch a background job to import orders."
    echo "Monitor the queue worker terminal for progress."
    
    php -r "
        require 'vendor/autoload.php';
        \$app = require_once 'bootstrap/app.php';
        \$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
        
        // Dispatch job to import 200 orders
        App\Jobs\SyncYoucanOrders::dispatch(1, [
            'limit' => 200,
            'page' => 1
        ]);
        
        echo '✅ Job dispatched successfully!' . PHP_EOL;
        echo 'Monitor progress with: tail -f storage/logs/laravel.log | grep -i youcan' . PHP_EOL;
    "
}

# Check import progress
check_progress() {
    print_info "Checking import progress..."
    php -r "
        require 'vendor/autoload.php';
        \$app = require_once 'bootstrap/app.php';
        \$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap();
        
        \$count = App\Models\Order::where('source', 'youcan')->count();
        \$customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count();
        \$latest = App\Models\Order::where('source', 'youcan')->latest()->first();
        
        echo '📊 Import Statistics:' . PHP_EOL;
        echo '  - Orders imported: ' . \$count . PHP_EOL;
        echo '  - Customers created: ' . \$customerCount . PHP_EOL;
        echo '  - Latest order: ' . (\$latest ? \$latest->ref . ' (' . \$latest->created_at . ')' : 'None') . PHP_EOL;
    "
}

# Setup webhook testing
setup_webhook() {
    print_info "Setting up webhook testing..."
    echo "Make sure ngrok is running with: ngrok http 8000"
    echo "Then update your .env file with the ngrok HTTPS URL"
    echo ""
    echo "To subscribe to webhooks, visit your app and go to:"
    echo "YouCan Import page → Subscribe to Webhooks"
    echo ""
    echo "Or test webhook endpoint manually:"
    echo "curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \\"
    echo "     -H 'Content-Type: application/json' \\"
    echo "     -d '{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}'"
}

# Monitor logs
monitor_logs() {
    print_info "Starting log monitoring..."
    echo "Press Ctrl+C to stop monitoring"
    tail -f storage/logs/laravel.log | grep -i --color=always "youcan\|order\|webhook"
}

# Main menu
show_menu() {
    echo ""
    echo "Choose an option:"
    echo "1) Check Laravel & Queue status"
    echo "2) Test YouCan API connection"
    echo "3) Import 200 orders from YouCan"
    echo "4) Check import progress"
    echo "5) Setup webhook testing"
    echo "6) Monitor logs"
    echo "7) Run all tests"
    echo "0) Exit"
    echo ""
}

# Run all tests
run_all_tests() {
    print_info "Running all tests..."
    check_laravel
    check_queue
    test_connection
    import_orders
    echo ""
    print_status "All tests initiated!"
    print_info "Monitor progress with option 6 (Monitor logs)"
    print_info "Check results with option 4 (Check import progress)"
}

# Main script loop
while true; do
    show_menu
    read -p "Enter your choice [0-7]: " choice
    
    case $choice in
        1)
            check_laravel
            check_queue
            ;;
        2)
            test_connection
            ;;
        3)
            import_orders
            ;;
        4)
            check_progress
            ;;
        5)
            setup_webhook
            ;;
        6)
            monitor_logs
            ;;
        7)
            run_all_tests
            ;;
        0)
            print_status "Goodbye!"
            exit 0
            ;;
        *)
            print_error "Invalid option. Please choose 0-7."
            ;;
    esac
    
    echo ""
    read -p "Press Enter to continue..."
done
