import { Head } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Edit, ArrowLeft } from 'lucide-react';

interface Supplier {
    id: number;
    name: string;
    code: string;
    contact_person: string | null;
    phone: string | null;
    email: string | null;
    address: string | null;
    status: 'active' | 'inactive';
}

interface Props {
    supplier: Supplier;
}

export default function Show({ supplier }: Props) {
    return (
        <AppSidebarLayout>
            <Head title={`Supplier: ${supplier.name}`} />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="sm"
                            onClick={() => window.history.back()}
                        >
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Back
                        </Button>
                        <h1 className="text-2xl font-semibold">Supplier Details</h1>
                    </div>
                    
                    <Button asChild>
                        <a href={route('suppliers.edit', supplier.id)}>
                            <Edit className="w-4 h-4 mr-2" />
                            Edit Supplier
                        </a>
                    </Button>
                </div>
                
                <Card>
                    <CardHeader>
                        <CardTitle>Basic Information</CardTitle>
                        <CardDescription>
                            View the basic information of the supplier.
                        </CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Supplier Name</h3>
                                <p className="text-lg">{supplier.name}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Supplier Code</h3>
                                <p className="text-lg">{supplier.code}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Contact Person</h3>
                                <p className="text-lg">{supplier.contact_person || '-'}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Phone Number</h3>
                                <p className="text-lg">{supplier.phone || '-'}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Email Address</h3>
                                <p className="text-lg">{supplier.email || '-'}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground mb-1">Status</h3>
                                <Badge variant={supplier.status === 'active' ? 'success' : 'secondary'} className="capitalize">
                                    {supplier.status}
                                </Badge>
                            </div>
                        </div>
                        
                        <div>
                            <h3 className="text-sm font-medium text-muted-foreground mb-2">Address</h3>
                            <p className="text-sm whitespace-pre-wrap">{supplier.address || '-'}</p>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </AppSidebarLayout>
    );
} 