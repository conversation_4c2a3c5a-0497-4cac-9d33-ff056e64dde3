<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncYoucanOrders;

class SyncHistoricalOrders extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'youcan:sync-historical 
                            {--company-id=1 : Company ID to sync for}
                            {--date-max= : Maximum date (YYYY-MM-DD)}
                            {--date-min= : Minimum date (YYYY-MM-DD)}
                            {--limit=100 : Orders per page}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync historical YouCan orders for a specific date range';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->option('company-id');
        $dateMax = $this->option('date-max') ?? '2025-06-10'; // Before today
        $dateMin = $this->option('date-min');
        $limit = $this->option('limit') ?? 100;

        $this->info("🚀 Dispatching historical YouCan order sync...");
        $this->newLine();

        $params = [
            'created_at_max' => $dateMax,
            'page' => 1,
            'limit' => $limit
        ];

        if ($dateMin) {
            $params['created_at_min'] = $dateMin;
        }

        $this->info("📋 Sync Parameters:");
        $this->line("   Company ID: {$companyId}");
        $this->line("   Date range: " . ($dateMin ?? 'All time') . " to {$dateMax}");
        $this->line("   Limit per page: {$limit}");
        $this->newLine();

        try {
            SyncYoucanOrders::dispatch($companyId, $params)->onQueue('youcan-sync');
            
            $this->info('✅ Historical order sync job dispatched successfully!');
            $this->line('💡 This will import orders from before your current import date');
            $this->line('💡 Start the queue worker: php artisan queue:work --queue=youcan-sync');
            $this->newLine();
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to dispatch sync job');
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
