<?php

namespace App\Services;

use App\Models\Country;
use App\Models\State;
use App\Models\City;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class GeographicMappingService
{
    /**
     * Cache duration for geographic lookups (1 hour)
     */
    const CACHE_DURATION = 3600;

    /**
     * Resolve geographic IDs from YouCan address data
     */
    public function resolveGeographicIds(array $addressData): array
    {
        $countryId = $this->resolveCountryId($addressData);
        $stateId = $this->resolveStateId($addressData, $countryId);
        $cityId = $this->resolveCityId($addressData, $countryId, $stateId);

        // Apply fallbacks for required fields that cannot be null
        $fallbacks = $this->getFallbackIds();

        return [
            'country_id' => $countryId ?? $fallbacks['country_id'],
            'state_id' => $stateId ?? $fallbacks['state_id'],
            'city_id' => $cityId ?? $fallbacks['city_id'],
        ];
    }

    /**
     * Get fallback geographic IDs for when resolution fails
     */
    protected function getFallbackIds(): array
    {
        return Cache::remember('geographic_fallbacks', self::CACHE_DURATION, function () {
            // Get first available country (preferably Tunisia since it's mentioned in the code)
            $country = Country::where('iso2', 'TN')->first() ?? Country::first();

            // Get first available state for the country
            $state = null;
            if ($country) {
                // Fix: Use country_code instead of country_id for State model
                $state = State::where('country_code', $country->iso2)->first();
            }

            // Get first available city for the state, or any city if no state
            $city = null;
            if ($state) {
                $city = City::where('state_id', $state->id)->first();
            } else {
                $city = City::first();
            }

            // Ensure we always have valid IDs - never return null for required fields
            $fallbackCountryId = $country?->iso2 ?? 'TN';
            $fallbackStateId = $state?->id ?? 1;
            $fallbackCityId = $city?->id ?? 1;

            // Log fallback values for debugging
            \Log::info('Geographic fallback IDs generated:', [
                'country_id' => $fallbackCountryId,
                'state_id' => $fallbackStateId,
                'city_id' => $fallbackCityId,
                'country_name' => $country?->name ?? 'Unknown',
                'state_name' => $state?->name ?? 'Unknown',
                'city_name' => $city?->name ?? 'Unknown',
            ]);

            return [
                'country_id' => $fallbackCountryId,
                'state_id' => $fallbackStateId,
                'city_id' => $fallbackCityId,
            ];
        });
    }

    /**
     * Resolve country ID from address data
     */
    protected function resolveCountryId(array $addressData): ?string
    {
        $countryCode = $addressData['country_code'] ?? null;
        $countryName = $addressData['country_name'] ?? null;

        // Try by ISO2 code first (most reliable)
        if ($countryCode) {
            $cacheKey = "country_by_code_{$countryCode}";
            
            return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($countryCode) {
                $country = Country::where('iso2', strtoupper($countryCode))->first();
                return $country ? $country->iso2 : null;
            });
        }

        // Try by name if code not available
        if ($countryName) {
            $cacheKey = "country_by_name_" . md5(strtolower($countryName));
            
            return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($countryName) {
                // Try exact match first
                $country = Country::where('name', $countryName)->first();
                
                if (!$country) {
                    // Try partial match
                    $country = Country::where('name', 'LIKE', '%' . $countryName . '%')->first();
                }
                
                return $country ? $country->iso2 : null;
            });
        }

        return null;
    }

    /**
     * Resolve state ID from address data
     */
    protected function resolveStateId(array $addressData, ?string $countryId): ?int
    {
        if (!$countryId || empty($addressData['state'])) {
            return null;
        }

        $stateName = $addressData['state'];
        $cacheKey = "state_{$countryId}_" . md5(strtolower($stateName));

        return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($stateName, $countryId) {
            // Try exact match first
            $state = State::where('country_code', $countryId)
                ->where('name', $stateName)
                ->first();

            if (!$state) {
                // Try partial match
                $state = State::where('country_code', $countryId)
                    ->where('name', 'LIKE', '%' . $stateName . '%')
                    ->first();
            }

            if (!$state) {
                // Try by fips_code or iso2 if available
                $state = State::where('country_code', $countryId)
                    ->where(function($query) use ($stateName) {
                        $query->where('fips_code', 'LIKE', '%' . $stateName . '%')
                              ->orWhere('iso2', 'LIKE', '%' . $stateName . '%');
                    })
                    ->first();
            }

            return $state ? $state->id : null;
        });
    }

    /**
     * Resolve city ID from address data
     */
    protected function resolveCityId(array $addressData, ?string $countryId, ?int $stateId): ?int
    {
        if (empty($addressData['city'])) {
            return null;
        }

        $cityName = $addressData['city'];

        // If we have a state, search within that state
        if ($stateId) {
            $cacheKey = "city_state_{$stateId}_" . md5(strtolower($cityName));
            
            return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($cityName, $stateId) {
                // Try exact match first
                $city = City::where('state_id', $stateId)
                    ->where('name', $cityName)
                    ->first();

                if (!$city) {
                    // Try partial match
                    $city = City::where('state_id', $stateId)
                        ->where('name', 'LIKE', '%' . $cityName . '%')
                        ->first();
                }

                return $city ? $city->id : null;
            });
        }

        // If no state but we have country, search within country
        if ($countryId) {
            $cacheKey = "city_country_{$countryId}_" . md5(strtolower($cityName));
            
            return Cache::remember($cacheKey, self::CACHE_DURATION, function () use ($cityName, $countryId) {
                $city = City::whereHas('state', function($query) use ($countryId) {
                    $query->where('country_code', $countryId);
                })
                ->where('name', 'LIKE', '%' . $cityName . '%')
                ->first();

                return $city ? $city->id : null;
            });
        }

        return null;
    }

    /**
     * Get geographic information for logging/debugging
     */
    public function getGeographicInfo(array $geoIds): array
    {
        $info = [];

        if ($geoIds['country_id']) {
            $country = Country::find($geoIds['country_id']);
            $info['country'] = $country ? $country->name : 'Unknown';
        }

        if ($geoIds['state_id']) {
            $state = State::find($geoIds['state_id']);
            $info['state'] = $state ? $state->name : 'Unknown';
        }

        if ($geoIds['city_id']) {
            $city = City::find($geoIds['city_id']);
            $info['city'] = $city ? $city->name : 'Unknown';
        }

        return $info;
    }

    /**
     * Clear geographic cache (useful for testing or data updates)
     */
    public function clearCache(): void
    {
        $patterns = [
            'country_by_code_*',
            'country_by_name_*',
            'state_*',
            'city_state_*',
            'city_country_*'
        ];

        foreach ($patterns as $pattern) {
            Cache::forget($pattern);
        }

        // Also clear fallback cache
        Cache::forget('geographic_fallbacks');
    }

    /**
     * Validate and suggest corrections for unmatched geographic data
     */
    public function validateAndSuggest(array $addressData): array
    {
        $suggestions = [];
        $geoIds = $this->resolveGeographicIds($addressData);

        // Check if country was resolved
        if (!$geoIds['country_id'] && !empty($addressData['country_name'])) {
            $suggestions['country'] = $this->suggestCountries($addressData['country_name']);
        }

        // Check if state was resolved
        if ($geoIds['country_id'] && !$geoIds['state_id'] && !empty($addressData['state'])) {
            $suggestions['state'] = $this->suggestStates($addressData['state'], $geoIds['country_id']);
        }

        // Check if city was resolved
        if (!$geoIds['city_id'] && !empty($addressData['city'])) {
            $suggestions['city'] = $this->suggestCities(
                $addressData['city'], 
                $geoIds['country_id'], 
                $geoIds['state_id']
            );
        }

        return [
            'resolved' => $geoIds,
            'suggestions' => $suggestions
        ];
    }

    /**
     * Suggest similar countries
     */
    protected function suggestCountries(string $countryName, int $limit = 3): array
    {
        return Country::where('name', 'LIKE', '%' . $countryName . '%')
            ->limit($limit)
            ->pluck('name', 'iso2')
            ->toArray();
    }

    /**
     * Suggest similar states
     */
    protected function suggestStates(string $stateName, string $countryId, int $limit = 3): array
    {
        return State::where('country_code', $countryId)
            ->where('name', 'LIKE', '%' . $stateName . '%')
            ->limit($limit)
            ->pluck('name', 'id')
            ->toArray();
    }

    /**
     * Suggest similar cities
     */
    protected function suggestCities(string $cityName, ?string $countryId = null, ?int $stateId = null, int $limit = 3): array
    {
        $query = City::where('name', 'LIKE', '%' . $cityName . '%');

        if ($stateId) {
            $query->where('state_id', $stateId);
        } elseif ($countryId) {
            $query->whereHas('state', function($q) use ($countryId) {
                $q->where('country_code', $countryId);
            });
        }

        return $query->limit($limit)
            ->pluck('name', 'id')
            ->toArray();
    }
}
