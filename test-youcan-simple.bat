@echo off
REM Simple YouCan Testing Script
echo.
echo 🚀 YouCan Simple Test
echo ====================
echo.

REM Check if we're in the right directory
if not exist "artisan" (
    echo ❌ Error: artisan file not found!
    echo Please run this script from the Laravel project root directory.
    pause
    exit /b 1
)

echo ✅ Found artisan file - we're in the right directory
echo.

:menu
echo Choose a test:
echo 1) Test YouCan API connection
echo 2) Import 200 orders (using artisan command)
echo 3) Check import progress (simple)
echo 4) Show help for webhook testing
echo 0) Exit
echo.
set /p choice="Enter your choice [0-4]: "

if "%choice%"=="1" goto test_connection
if "%choice%"=="2" goto import_orders
if "%choice%"=="3" goto check_progress
if "%choice%"=="4" goto webhook_help
if "%choice%"=="0" goto exit
echo Invalid choice. Please try again.
goto menu

:test_connection
echo.
echo ℹ️  Testing YouCan API connection...
php artisan youcan:test-import --company-id=1 --test-connection
goto continue

:import_orders
echo.
echo ℹ️  Starting YouCan order import...
echo This will use the interactive artisan command.
echo When prompted for number of orders, enter: 200
echo.
php artisan youcan:test-import --company-id=1
goto continue

:check_progress
echo.
echo ℹ️  Checking import progress...
echo Opening Laravel tinker to check orders...
echo.
echo Copy and paste this command in tinker:
echo App\Models\Order::where('source', 'youcan')->count()
echo.
php artisan tinker
goto continue

:webhook_help
echo.
echo ℹ️  Webhook Testing Help
echo =====================
echo.
echo 1. Start ngrok: ngrok http 8000
echo 2. Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
echo 3. Update .env file:
echo    YOUCAN_NGROK_URL=https://abc123.ngrok.io
echo    APP_URL=https://abc123.ngrok.io
echo.
echo 4. Test webhook endpoint:
echo    Visit: http://localhost:8000/youcan/import
echo    Click "Subscribe to Webhooks"
echo.
echo 5. Or test manually with curl:
echo    curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \
echo         -H "Content-Type: application/json" \
echo         -d "{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}"
goto continue

:continue
echo.
pause
goto menu

:exit
echo.
echo ✅ Goodbye!
pause
exit /b 0
