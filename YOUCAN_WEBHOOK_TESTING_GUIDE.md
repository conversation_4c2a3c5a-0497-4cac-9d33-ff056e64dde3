# YouCan REST Webhook Testing with ngrok - Complete Guide

## 🎯 Overview

This guide provides step-by-step instructions for testing YouCan REST webhooks locally using ngrok. This setup allows you to receive real-time webhook notifications from YouCan when orders are created, updated, or other events occur.

## 📋 Prerequisites

- Laravel application running locally
- YouCan store with API access
- ngrok account (free tier works)
- YouCan integration configured in your app

## 🚀 Step-by-Step Setup

### **Phase 1: Install and Configure ngrok**

#### 1. Install ngrok
```bash
# Option 1: Download from https://ngrok.com/download
# Option 2: Package managers
# Windows (Chocolatey)
choco install ngrok

# macOS (Homebrew)
brew install ngrok

# Linux (Snap)
snap install ngrok
```

#### 2. Sign up and get auth token
```bash
# Sign up at https://ngrok.com
# Get your auth token from dashboard
ngrok config add-authtoken YOUR_AUTH_TOKEN_HERE
```

#### 3. Verify installation
```bash
ngrok version
# Should show version info
```

### **Phase 2: Laravel Application Setup**

#### 4. Update environment variables
```env
# Add to your .env file
YOUCAN_NGROK_URL=https://your-subdomain.ngrok.io
APP_URL=https://your-subdomain.ngrok.io

# Optional: Set default company for testing
YOUCAN_DEFAULT_COMPANY_ID=1

# Note: No YOUCAN_WEBHOOK_SECRET needed - uses OAuth client_secret from database
```

#### 5. Start Laravel development server
```bash
# Terminal 1: Start Laravel
php artisan serve --host=0.0.0.0 --port=8000

# Keep this terminal running
```

#### 6. Start ngrok tunnel
```bash
# Terminal 2: Start ngrok
ngrok http 8000

# For custom subdomain (paid plan)
ngrok http 8000 --subdomain=your-app-name

# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
```

#### 7. Update ngrok URL in environment
```bash
# Update .env with the ngrok URL
YOUCAN_NGROK_URL=https://abc123.ngrok.io
APP_URL=https://abc123.ngrok.io

# Restart Laravel if needed
```

### **Phase 3: Test Webhook Endpoint**

#### 8. Test endpoint accessibility
```bash
# Test webhook endpoint
php artisan youcan:setup-webhook --test --ngrok-url=https://abc123.ngrok.io

# Should return success if endpoint is accessible
```

#### 9. Manual endpoint test
```bash
# Alternative manual test
curl -X POST https://abc123.ngrok.io/api/youcan/webhook \
     -H "Content-Type: application/json" \
     -d '{"test": "data", "event": "test.webhook"}'

# Check Laravel logs for the request
tail -f storage/logs/laravel.log
```

### **Phase 4: Configure YouCan Webhook**

#### 10. Setup webhook subscription
```bash
# Setup webhook for specific company
php artisan youcan:setup-webhook --company-id=1 --ngrok-url=https://abc123.ngrok.io

# Setup with specific event
php artisan youcan:setup-webhook --company-id=1 --event=order.create --ngrok-url=https://abc123.ngrok.io

# List current webhooks
php artisan youcan:setup-webhook --company-id=1 --list

# Unsubscribe from webhooks
php artisan youcan:setup-webhook --company-id=1 --unsubscribe
```

#### 11. Verify webhook subscription
```bash
# Check webhook status
php artisan youcan:setup-webhook --company-id=1 --list

# Should show your ngrok URL in the webhook list
```

### **Phase 5: Testing & Monitoring**

#### 12. Setup monitoring
```bash
# Terminal 3: Monitor Laravel logs
tail -f storage/logs/laravel.log

# Terminal 4: Monitor queue jobs (if using queues)
php artisan queue:work

# ngrok Web Interface: http://127.0.0.1:4040
# Shows all HTTP requests to your tunnel
```

#### 13. Create test orders
- Login to your YouCan admin panel
- Create test orders in your store
- Monitor the webhook delivery in real-time

#### 14. Verify webhook processing
```bash
# Check Laravel logs for webhook processing
grep "YouCan webhook" storage/logs/laravel.log

# Check database for imported orders
php artisan tinker
>>> App\Models\Order::where('source', 'youcan')->latest()->take(5)->get()
```

## 🔧 Troubleshooting

### **Common Issues & Solutions**

#### **Issue 1: Webhook endpoint not accessible**
```bash
# Check if Laravel is running
curl http://localhost:8000

# Check if ngrok tunnel is active
curl https://your-subdomain.ngrok.io

# Verify webhook route exists
php artisan route:list | grep webhook
```

#### **Issue 2: Webhook not receiving data**
```bash
# Check ngrok dashboard for requests
# Visit: http://127.0.0.1:4040

# Verify webhook subscription
php artisan youcan:setup-webhook --company-id=1 --list

# Check YouCan webhook logs in admin panel
```

#### **Issue 3: Authentication errors**
```bash
# Verify YouCan credentials
php artisan youcan:test-import --company-id=1

# Check integration status
# Visit: /admin/settings/cms-integrations
```

#### **Issue 4: Orders not importing**
```bash
# Check webhook processing logs
grep "ProcessYoucanWebhook" storage/logs/laravel.log

# Manually test order import
php artisan youcan:test-import --company-id=1

# Check queue jobs
php artisan queue:failed
```

## 📊 Monitoring Commands

### **Real-time Monitoring**
```bash
# Monitor all webhook activity
tail -f storage/logs/laravel.log | grep -i webhook

# Monitor order processing
tail -f storage/logs/laravel.log | grep -i "order"

# Monitor YouCan API calls
tail -f storage/logs/laravel.log | grep -i "youcan"
```

### **Database Monitoring**
```bash
# Check recent orders
php artisan tinker
>>> App\Models\Order::where('source', 'youcan')->latest()->take(10)->get(['id', 'ref', 'youcan_order_id', 'created_at'])

# Check webhook processing logs
>>> App\Models\CmsSyncLog::where('action', 'webhook_received')->latest()->take(5)->get()
```

## 🎯 Testing Scenarios

### **Scenario 1: New Order Creation**
1. Create new order in YouCan admin
2. Webhook should trigger immediately
3. Order should appear in your database
4. Customer should be created/matched
5. Order items should be processed

### **Scenario 2: Order Status Update**
1. Update order status in YouCan
2. Webhook should trigger with update event
3. Local order status should sync

### **Scenario 3: Duplicate Order Prevention**
1. Create order in YouCan
2. Manually trigger import again
3. Should not create duplicate order
4. Should update existing order with YouCan ID

## 🔐 Security Considerations

### **Webhook Security**
```php
// Webhook signature verification is automatically handled
// The system uses OAuth client_secret from database integration
// No additional YOUCAN_WEBHOOK_SECRET needed in .env file
$signature = $request->header('x-youcan-signature');
$payload = $request->getContent();
$clientSecret = $integration->getCredential('client_secret');
$expectedSignature = hash_hmac('sha256', json_encode($payload), $clientSecret);

if (!hash_equals($signature, $expectedSignature)) {
    abort(401, 'Invalid webhook signature');
}
```

### **ngrok Security**
- Use HTTPS URLs only
- Don't expose sensitive data in logs
- Use webhook secrets for verification
- Limit webhook events to necessary ones

## 📈 Performance Tips

### **Optimize Webhook Processing**
```php
// Use queued jobs for heavy processing
ProcessYoucanWebhook::dispatch($webhookData)->onQueue('webhooks');

// Batch process multiple orders
SyncYoucanOrders::dispatch($companyId, ['limit' => 50]);

// Use database transactions
DB::transaction(function() {
    // Process webhook data
});
```

### **Monitor Performance**
```bash
# Monitor queue performance
php artisan queue:monitor

# Check processing times
grep "Processing time" storage/logs/laravel.log

# Monitor memory usage
php artisan tinker
>>> memory_get_peak_usage(true) / 1024 / 1024 . ' MB'
```

## 🎉 Success Indicators

✅ **Webhook Setup Complete When:**
- ngrok tunnel shows HTTPS URL
- Laravel logs show webhook requests
- YouCan webhook list shows your URL
- Test orders trigger webhook delivery
- Orders appear in your database
- No errors in Laravel logs

✅ **Production Ready When:**
- All test scenarios pass
- Error handling works correctly
- Performance is acceptable
- Security measures implemented
- Monitoring is in place

## 📞 Support

If you encounter issues:
1. Check Laravel logs first
2. Verify ngrok tunnel status
3. Test webhook endpoint manually
4. Check YouCan API credentials
5. Review webhook subscription status

This setup provides a robust foundation for testing YouCan webhooks locally before deploying to production! 🚀
