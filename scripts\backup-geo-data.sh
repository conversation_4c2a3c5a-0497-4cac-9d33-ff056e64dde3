#!/bin/bash

# Backup Geographic Data Script
# Usage: ./scripts/backup-geo-data.sh

echo "🌍 Backing up geographic data..."

# Get database connection details from .env
DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
DB_PORT=$(grep DB_PORT .env | cut -d '=' -f2)
DB_DATABASE=$(grep DB_DATABASE .env | cut -d '=' -f2)
DB_USERNAME=$(grep DB_USERNAME .env | cut -d '=' -f2)
DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d '=' -f2)

# Create backup directory if it doesn't exist
mkdir -p storage/backups

# Backup countries, states, and cities tables
echo "📦 Backing up countries table..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -t countries --data-only --inserts > storage/backups/countries.sql

echo "📦 Backing up states table..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -t states --data-only --inserts > storage/backups/states.sql

echo "📦 Backing up cities table..."
pg_dump -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -t cities --data-only --inserts > storage/backups/cities.sql

echo "✅ Geographic data backup completed!"
echo "📁 Backup files saved in storage/backups/"
