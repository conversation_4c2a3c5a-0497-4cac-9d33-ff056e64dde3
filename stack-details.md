# Order Management System - Seller Panel Stack Details

## Technology Stack

### Backend
- **Framework**: <PERSON><PERSON> 12
- **Database**: PostgreSQL
- **ORM**: Eloquent
- **Authentication**: Laravel Sanctum
- **Multi-tenancy**: Single database with company_id column

### Frontend
- **Framework**: React with TypeScript
- **UI Library**: ShadCN UI (based on Tailwind CSS)
- **Routing**: Inertia.js (server-side routing with SPA experience)
- **State Management**: React Context API
- **Form Handling**: Inertia.js useForm hook
- **Icons**: Lucide React

## Architecture Overview

This application is a seller panel for a multi-tenant order management system. It uses Inertia.js to bridge Laravel and React, providing a seamless SPA experience while leveraging <PERSON><PERSON>'s routing and controllers.

### Key Features
- Company-specific authentication and user management
- Role-based access control within companies
- Order management and tracking
- Product catalog management
- Customer management
- Supplier management
- Warehouse management
- Ad platform and campaign tracking

### Development Principles
- SOLID principles for clean, maintainable code
- Reusable components with ShadCN UI design system
- Proper form validation and error handling
- Company-scoped data access
- Responsive layouts and intuitive workflows
- Strong TypeScript typing

## Module Structure
The application is organized into several modules, each with its own set of features and database tables:
- Users & Roles
- Orders & Order Statuses
- Products
- Customers
- Suppliers
- Warehouses
- Ad Platforms & Campaigns
- Settings

Each module has its own detailed specification document in this repository.
