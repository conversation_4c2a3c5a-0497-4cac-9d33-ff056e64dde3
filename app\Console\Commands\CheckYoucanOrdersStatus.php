<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Order;
use App\Models\CompanyCmsIntegration;
use App\Models\CmsPlatform;
use App\Services\CmsIntegration\YoucanCmsService;

class CheckYoucanOrdersStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'youcan:check-orders-status {company_id?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of YouCan orders import for debugging';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $companyId = $this->argument('company_id');
        
        if (!$companyId) {
            $companyId = $this->ask('Enter company ID');
        }

        $this->info("🔍 Checking YouCan orders status for company ID: {$companyId}");
        $this->newLine();

        // Check local orders
        $localOrders = Order::where('company_id', $companyId)
            ->where('source', 'youcan')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get(['id', 'ref', 'youcan_order_id', 'total_price', 'created_at']);

        $totalLocalOrders = Order::where('company_id', $companyId)
            ->where('source', 'youcan')
            ->count();

        $this->info("📊 Local YouCan Orders Summary:");
        $this->line("Total YouCan orders in database: {$totalLocalOrders}");
        
        if ($localOrders->count() > 0) {
            $this->line("Recent 10 orders:");
            $this->table(
                ['ID', 'Ref', 'YouCan ID', 'Total', 'Created At'],
                $localOrders->map(function ($order) {
                    return [
                        $order->id,
                        $order->ref,
                        $order->youcan_order_id,
                        $order->total_price,
                        $order->created_at->format('Y-m-d H:i:s')
                    ];
                })->toArray()
            );
        } else {
            $this->warn("No YouCan orders found in local database");
        }

        $this->newLine();

        // Check YouCan integration
        $platform = CmsPlatform::bySlug('youcan')->first();
        if (!$platform) {
            $this->error("❌ YouCan platform not found in database");
            return;
        }

        $integration = CompanyCmsIntegration::forCompany($companyId)
            ->forPlatform($platform->id)
            ->first();

        if (!$integration) {
            $this->error("❌ YouCan integration not found for company {$companyId}");
            return;
        }

        $this->info("✅ YouCan integration found:");
        $this->line("Integration ID: {$integration->id}");
        $this->line("Status: " . ($integration->is_active ? 'Active' : 'Inactive'));
        $this->line("Created: {$integration->created_at}");

        // Test API connection
        $this->newLine();
        $this->info("🔗 Testing YouCan API connection...");
        
        try {
            $youcanService = new YoucanCmsService($integration);
            $testResult = $youcanService->testConnection();
            
            if ($testResult) {
                $this->info("✅ YouCan API connection successful");
                
                // Get recent orders from API
                $this->info("📥 Fetching recent orders from YouCan API...");
                $apiOrders = $youcanService->getOrders(['page' => 1, 'limit' => 5]);
                
                if (!empty($apiOrders['data'])) {
                    $this->info("Found {$apiOrders['meta']['pagination']['total']} total orders in YouCan");
                    $this->line("Recent 5 orders from API:");
                    
                    $apiOrdersTable = [];
                    foreach ($apiOrders['data'] as $order) {
                        $apiOrdersTable[] = [
                            $order['id'],
                            $order['ref'],
                            $order['total'] ?? 'N/A',
                            $order['status_new'] ?? 'N/A',
                            $order['created_at'] ?? 'N/A'
                        ];
                    }
                    
                    $this->table(
                        ['YouCan ID', 'Ref', 'Total', 'Status', 'Created At'],
                        $apiOrdersTable
                    );
                } else {
                    $this->warn("No orders found in YouCan API response");
                }
                
            } else {
                $this->error("❌ YouCan API connection failed");
            }
            
        } catch (\Exception $e) {
            $this->error("❌ Error testing YouCan API: " . $e->getMessage());
        }

        $this->newLine();
        $this->info("🎯 Recommendations:");
        
        if ($totalLocalOrders > 0) {
            $this->line("• You have {$totalLocalOrders} YouCan orders imported");
            $this->line("• If you're not seeing new orders, they might already exist");
            $this->line("• Try importing from a different date range");
            $this->line("• Use the 'Test Import' button to import 3 recent orders");
        } else {
            $this->line("• No YouCan orders found locally");
            $this->line("• Try running the import process");
            $this->line("• Check if your YouCan store has any orders");
        }
        
        $this->line("• Check the Laravel logs for detailed import information");
        $this->line("• Make sure the queue worker is running: php artisan queue:work");
    }
}
