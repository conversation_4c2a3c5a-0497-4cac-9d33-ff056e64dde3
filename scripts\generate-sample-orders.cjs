const XLSX = require('xlsx');

// Sample data for orders
const sampleOrders = [
    {
        "Client Name": "<PERSON>",
        "Mobile": "51234567",
        "Phone Code": "216",
        "Mobile 2": "52345678",
        "Phone Code 2": "216",
        "Address": "123 Main Street, Downtown",
        "City": "Tunis", // City name instead of ID
        "State": "Tunis", // State name instead of ID
        "Platform": "Facebook", // Platform name instead of ID
        "Campaign": "Summer Sale 2024", // Campaign name instead of ID
        "Status": "New", // Status name instead of ID
        "Notes": "Preferred delivery time: afternoon",
        "Delivery Notes": "Ring the doorbell twice",
        "Delivery Tax": "10",
        "Is Fragile": "true",
        "Can Open": "true",
        "Can Pay Cheque": "false"
    },
    {
        "Client Name": "<PERSON>",
        "Mobile": "53456789",
        "Phone Code": "216",
        "Mobile 2": "",
        "Phone Code 2": "",
        "Address": "456 Oak Avenue, Suburb Area",
        "City": "Sfax",
        "State": "Sfax",
        "Platform": "Instagram",
        "Campaign": "Spring Collection",
        "Status": "Pending",
        "Notes": "Call before delivery",
        "Delivery Notes": "Leave at reception",
        "Delivery Tax": "15",
        "Is Fragile": "false",
        "Can Open": "true",
        "Can Pay Cheque": "true"
    },
    {
        "Client Name": "Mohammed Ali",
        "Mobile": "54567890",
        "Phone Code": "216",
        "Mobile 2": "55678901",
        "Phone Code 2": "216",
        "Address": "789 Pine Road, City Center",
        "City": "Sousse",
        "State": "Sousse",
        "Platform": "Facebook",
        "Campaign": "Ramadan Offers",
        "Status": "Processing",
        "Notes": "Regular customer",
        "Delivery Notes": "Fragile items inside",
        "Delivery Tax": "12",
        "Is Fragile": "true",
        "Can Open": "false",
        "Can Pay Cheque": "false"
    },
    {
        "Client Name": "Emma Wilson",
        "Mobile": "56789012",
        "Phone Code": "216",
        "Mobile 2": "",
        "Phone Code 2": "",
        "Address": "321 Elm Street, North District",
        "City": "Bizerte",
        "State": "Bizerte",
        "Platform": "TikTok",
        "Campaign": "Flash Sale",
        "Status": "Confirmed",
        "Notes": "New customer",
        "Delivery Notes": "Contact on WhatsApp",
        "Delivery Tax": "8",
        "Is Fragile": "false",
        "Can Open": "true",
        "Can Pay Cheque": "true"
    },
    {
        "Client Name": "Ahmed Hassan",
        "Mobile": "57890123",
        "Phone Code": "216",
        "Mobile 2": "58901234",
        "Phone Code 2": "216",
        "Address": "567 Cedar Lane, East Side",
        "City": "Monastir",
        "State": "Monastir",
        "Platform": "Facebook",
        "Campaign": "Weekend Special",
        "Status": "New",
        "Notes": "VIP customer",
        "Delivery Notes": "Handle with care",
        "Delivery Tax": "20",
        "Is Fragile": "true",
        "Can Open": "true",
        "Can Pay Cheque": "false"
    }
];

// Create a new workbook
const wb = XLSX.utils.book_new();

// Convert the data to a worksheet
const ws = XLSX.utils.json_to_sheet(sampleOrders);

// Add the worksheet to the workbook
XLSX.utils.book_append_sheet(wb, ws, "Sample Orders");

// Write the workbook to a file
XLSX.writeFile(wb, "sample_orders_import.xlsx");

console.log("Sample orders Excel file has been generated successfully!"); 