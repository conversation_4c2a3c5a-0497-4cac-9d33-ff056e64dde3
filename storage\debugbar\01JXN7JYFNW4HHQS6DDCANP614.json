{"__meta": {"id": "01JXN7JYFNW4HHQS6DDCANP614", "datetime": "2025-06-13 18:07:15", "utime": **********.446495, "method": "GET", "uri": "/dashboard", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.606303, "end": **********.446514, "duration": 0.8402109146118164, "duration_str": "840ms", "measures": [{"label": "Booting", "start": **********.606303, "relative_start": 0, "end": **********.687713, "relative_end": **********.687713, "duration": 0.*****************, "duration_str": "81.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.687733, "relative_start": 0.*****************, "end": **********.446517, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "759ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.696582, "relative_start": 0.*****************, "end": **********.697959, "relative_end": **********.697959, "duration": 0.0013768672943115234, "duration_str": "1.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.291797, "relative_start": 0.****************, "end": **********.445309, "relative_end": **********.445309, "duration": 0.*****************, "duration_str": "154ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 4951312, "peak_usage_str": "5MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "12.x", "tooltip": {"Laravel Version": "12.7.2", "PHP Version": "8.2.13", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "Dashboard", "param_count": null, "params": [], "start": **********.446238, "type": "tsx", "hash": "tsxC:\\Users\\<USER>\\Desktop\\react-starter-kit\\resources\\js/Pages/Dashboard.tsxDashboard", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fresources%2Fjs%2FPages%2FDashboard.tsx&line=1", "ajax": false, "filename": "Dashboard.tsx", "line": "?"}}]}, "queries": {"count": 13, "nb_statements": 12, "nb_visible_statements": 13, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.13205, "accumulated_duration_str": "132ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "Connection Established", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, {"index": 8, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 112}, {"index": 9, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 92}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 60}, {"index": 11, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 88}], "start": **********.69595, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "CacheManager.php:226", "source": {"index": 7, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FCacheManager.php&line=226", "ajax": false, "filename": "CacheManager.php", "line": "226"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 0}, {"sql": "select * from \"sessions\" where \"id\" = 'fkYN1wqLjl45zY3fDczAVdqwKjktRo550uiBDKyv' limit 1", "type": "query", "params": [], "bindings": ["fkYN1wqLjl45zY3fDczAVdqwKjktRo550uiBDKyv"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 116}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 104}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Store.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 88}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 146}], "start": **********.738772, "duration": 0.05897, "duration_str": "58.97ms", "memory": 0, "memory_str": null, "filename": "DatabaseSessionHandler.php:96", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 96}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FSession%2FDatabaseSessionHandler.php&line=96", "ajax": false, "filename": "DatabaseSessionHandler.php", "line": "96"}, "connection": "order_management_system", "explain": null, "start_percent": 0, "width_percent": 44.657}, {"sql": "select * from \"users\" where \"id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 169}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 82}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 61}], "start": **********.807121, "duration": 0.00677, "duration_str": "6.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:58", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 58}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=58", "ajax": false, "filename": "EloquentUserProvider.php", "line": "58"}, "connection": "order_management_system", "explain": null, "start_percent": 44.657, "width_percent": 5.127}, {"sql": "select * from \"companies\" where \"companies\".\"id\" = 1 and \"companies\".\"deleted_at\" is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 86}], "start": **********.9252348, "duration": 0.007809999999999999, "duration_str": "7.81ms", "memory": 0, "memory_str": null, "filename": "LoadAppSettings.php:20", "source": {"index": 21, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FMiddleware%2FLoadAppSettings.php&line=20", "ajax": false, "filename": "LoadAppSettings.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 49.784, "width_percent": 5.914}, {"sql": "select * from \"cache\" where \"key\" in ('laravel_cache_app_settings_1')", "type": "query", "params": [], "bindings": ["laravel_cache_app_settings_1"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 118}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 455}, {"index": 18, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 25}], "start": **********.939312, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:129", "source": {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=129", "ajax": false, "filename": "DatabaseStore.php", "line": "129"}, "connection": "order_management_system", "explain": null, "start_percent": 55.699, "width_percent": 1.318}, {"sql": "delete from \"cache\" where \"key\" in ('laravel_cache_app_settings_1', 'laravel_cache_illuminate:cache:flexible:created:app_settings_1') and \"expiration\" <= **********", "type": "query", "params": [], "bindings": ["laravel_cache_app_settings_1", "laravel_cache_illuminate:cache:flexible:created:app_settings_1", **********], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 144}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 104}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 118}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 455}], "start": **********.945132, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:409", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 409}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=409", "ajax": false, "filename": "DatabaseStore.php", "line": "409"}, "connection": "order_management_system", "explain": null, "start_percent": 57.016, "width_percent": 1.234}, {"sql": "select * from \"currencies\" where \"company_id\" = 1 and \"is_default\" = 1 and \"currencies\".\"company_id\" = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 80}, {"index": 17, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}], "start": **********.955868, "duration": 0.005019999999999999, "duration_str": "5.02ms", "memory": 0, "memory_str": null, "filename": "AppSettings.php:80", "source": {"index": 16, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 80}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHelpers%2FAppSettings.php&line=80", "ajax": false, "filename": "AppSettings.php", "line": "80"}, "connection": "order_management_system", "explain": null, "start_percent": 58.251, "width_percent": 3.802}, {"sql": "select * from \"countries\" where \"iso2\" = 'TN' limit 1", "type": "query", "params": [], "bindings": ["TN"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 83}, {"index": 17, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 28}, {"index": 18, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 23}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}], "start": **********.984777, "duration": 0.009779999999999999, "duration_str": "9.78ms", "memory": 0, "memory_str": null, "filename": "AppSettings.php:83", "source": {"index": 16, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 83}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHelpers%2FAppSettings.php&line=83", "ajax": false, "filename": "AppSettings.php", "line": "83"}, "connection": "order_management_system", "explain": null, "start_percent": 62.052, "width_percent": 7.406}, {"sql": "select * from \"currencies\" where \"company_id\" = 1 and \"currencies\".\"company_id\" = 1 order by \"is_default\" desc, \"name\" asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 89}, {"index": 16, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Http/Middleware/LoadAppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Middleware\\LoadAppSettings.php", "line": 23}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 208}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Middleware/AddLinkHeadersForPreloadedAssets.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\AddLinkHeadersForPreloadedAssets.php", "line": 20}], "start": **********.0004041, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "AppSettings.php:89", "source": {"index": 15, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 89}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHelpers%2FAppSettings.php&line=89", "ajax": false, "filename": "AppSettings.php", "line": "89"}, "connection": "order_management_system", "explain": null, "start_percent": 69.459, "width_percent": 1.166}, {"sql": "insert into \"cache\" (\"expiration\", \"key\", \"value\") values (1749841635, 'laravel_cache_app_settings_1', 'a:6:{s:7:\"company\";a:5:{s:2:\"id\";i:1;s:4:\"name\";s:16:\"Acme Corporation\";s:5:\"email\";s:17:\"<EMAIL>\";s:5:\"phone\";s:17:\"+****************\";s:7:\"website\";N;}s:12:\"localization\";a:6:{s:16:\"default_currency\";s:3:\"TND\";s:15:\"default_country\";s:2:\"TN\";s:16:\"default_language\";s:2:\"en\";s:8:\"timezone\";s:3:\"UTC\";s:11:\"date_format\";s:5:\"d-m-Y\";s:11:\"time_format\";s:2:\"12\";}s:2:\"ui\";a:2:{s:19:\"datatable_row_limit\";i:10;s:25:\"employees_can_export_data\";b:1;}s:8:\"currency\";a:8:{s:2:\"id\";i:9;s:4:\"code\";s:3:\"TND\";s:4:\"name\";s:13:\"tunisan dinar\";s:6:\"symbol\";s:2:\"DT\";s:8:\"position\";s:5:\"right\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:3;}s:7:\"country\";a:5:{s:4:\"iso2\";s:2:\"TN\";s:4:\"name\";s:7:\"Tunisia\";s:9:\"phonecode\";s:3:\"216\";s:8:\"currency\";s:3:\"TND\";s:15:\"currency_symbol\";s:5:\"ت.د\";}s:10:\"currencies\";a:5:{i:0;a:9:{s:2:\"id\";i:9;s:4:\"code\";s:3:\"TND\";s:4:\"name\";s:13:\"tunisan dinar\";s:6:\"symbol\";s:2:\"DT\";s:8:\"position\";s:5:\"right\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:3;s:10:\"is_default\";b:1;}i:1;a:9:{s:2:\"id\";i:3;s:4:\"code\";s:3:\"GBP\";s:4:\"name\";s:13:\"British Pound\";s:6:\"symbol\";s:2:\"£\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:2;a:9:{s:2:\"id\";i:2;s:4:\"code\";s:3:\"EUR\";s:4:\"name\";s:4:\"Euro\";s:6:\"symbol\";s:3:\"€\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:3;a:9:{s:2:\"id\";i:4;s:4:\"code\";s:3:\"JPY\";s:4:\"name\";s:12:\"Japanese Yen\";s:6:\"symbol\";s:2:\"¥\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:4;a:9:{s:2:\"id\";i:1;s:4:\"code\";s:3:\"USD\";s:4:\"name\";s:9:\"US Dollar\";s:6:\"symbol\";s:1:\"$\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}}}') on conflict (\"key\") do update set \"expiration\" = \"excluded\".\"expiration\", \"key\" = \"excluded\".\"key\", \"value\" = \"excluded\".\"value\"", "type": "query", "params": [], "bindings": [1749841635, "laravel_cache_app_settings_1", "a:6:{s:7:\"company\";a:5:{s:2:\"id\";i:1;s:4:\"name\";s:16:\"Acme Corporation\";s:5:\"email\";s:17:\"<EMAIL>\";s:5:\"phone\";s:17:\"+****************\";s:7:\"website\";N;}s:12:\"localization\";a:6:{s:16:\"default_currency\";s:3:\"TND\";s:15:\"default_country\";s:2:\"TN\";s:16:\"default_language\";s:2:\"en\";s:8:\"timezone\";s:3:\"UTC\";s:11:\"date_format\";s:5:\"d-m-Y\";s:11:\"time_format\";s:2:\"12\";}s:2:\"ui\";a:2:{s:19:\"datatable_row_limit\";i:10;s:25:\"employees_can_export_data\";b:1;}s:8:\"currency\";a:8:{s:2:\"id\";i:9;s:4:\"code\";s:3:\"TND\";s:4:\"name\";s:13:\"tunisan dinar\";s:6:\"symbol\";s:2:\"DT\";s:8:\"position\";s:5:\"right\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:3;}s:7:\"country\";a:5:{s:4:\"iso2\";s:2:\"TN\";s:4:\"name\";s:7:\"Tunisia\";s:9:\"phonecode\";s:3:\"216\";s:8:\"currency\";s:3:\"TND\";s:15:\"currency_symbol\";s:5:\"ت.د\";}s:10:\"currencies\";a:5:{i:0;a:9:{s:2:\"id\";i:9;s:4:\"code\";s:3:\"TND\";s:4:\"name\";s:13:\"tunisan dinar\";s:6:\"symbol\";s:2:\"DT\";s:8:\"position\";s:5:\"right\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:3;s:10:\"is_default\";b:1;}i:1;a:9:{s:2:\"id\";i:3;s:4:\"code\";s:3:\"GBP\";s:4:\"name\";s:13:\"British Pound\";s:6:\"symbol\";s:2:\"£\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:2;a:9:{s:2:\"id\";i:2;s:4:\"code\";s:3:\"EUR\";s:4:\"name\";s:4:\"Euro\";s:6:\"symbol\";s:3:\"€\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:3;a:9:{s:2:\"id\";i:4;s:4:\"code\";s:3:\"JPY\";s:4:\"name\";s:12:\"Japanese Yen\";s:6:\"symbol\";s:2:\"¥\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}i:4;a:9:{s:2:\"id\";i:1;s:4:\"code\";s:3:\"USD\";s:4:\"name\";s:9:\"US Dollar\";s:6:\"symbol\";s:1:\"$\";s:8:\"position\";s:4:\"left\";s:18:\"thousand_separator\";s:1:\",\";s:17:\"decimal_separator\";s:1:\".\";s:8:\"decimals\";i:2;s:10:\"is_default\";b:0;}}}"], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 166}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 240}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 455}, {"index": 15, "namespace": null, "name": "app/Helpers/AppSettings.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Helpers\\AppSettings.php", "line": 29}], "start": **********.035741, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "DatabaseStore.php:189", "source": {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/DatabaseStore.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\DatabaseStore.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FCache%2FDatabaseStore.php&line=189", "ajax": false, "filename": "DatabaseStore.php", "line": "189"}, "connection": "order_management_system", "explain": null, "start_percent": 70.625, "width_percent": 1.734}, {"sql": "select count(*) as aggregate from \"users\" where \"company_id\" = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.0621948, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:20", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=20", "ajax": false, "filename": "DashboardController.php", "line": "20"}, "connection": "order_management_system", "explain": null, "start_percent": 72.359, "width_percent": 1.098}, {"sql": "select a.attname as name, t.typname as type_name, format_type(a.atttypid, a.atttypmod) as type, (select tc.collcollate from pg_catalog.pg_collation tc where tc.oid = a.attcollation) as collation, not a.attnotnull as nullable, (select pg_get_expr(adbin, adrelid) from pg_attrdef where c.oid = pg_attrdef.adrelid and pg_attrdef.adnum = a.attnum) as default, a.attgenerated as generated, col_description(c.oid, a.attnum) as comment from pg_attribute a, pg_class c, pg_type t, pg_namespace n where c.relname = 'users' and n.nspname = current_schema() and a.attnum > 0 and a.attrelid = c.oid and a.atttypid = t.oid and n.oid = c.relnamespace order by a.attnum", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 25}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2317562, "duration": 0.032229999999999995, "duration_str": "32.23ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:25", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=25", "ajax": false, "filename": "DashboardController.php", "line": "25"}, "connection": "order_management_system", "explain": null, "start_percent": 73.457, "width_percent": 24.407}, {"sql": "select count(*) as aggregate from \"users\" where \"company_id\" = 1 and \"last_active_at\" >= '2025-06-13 18:02:15'", "type": "query", "params": [], "bindings": [1, "2025-06-13 18:02:15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 28}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 265}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 211}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 808}], "start": **********.2774, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:28", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\react-starter-kit\\app\\Http\\Controllers\\DashboardController.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=28", "ajax": false, "filename": "DashboardController.php", "line": "28"}, "connection": "order_management_system", "explain": null, "start_percent": 97.864, "width_percent": 2.136}]}, "models": {"data": {"App\\Models\\Currency": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Company": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCompany.php&line=1", "ajax": false, "filename": "Company.php", "line": "?"}}, "App\\Models\\Country": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FModels%2FCountry.php&line=1", "ajax": false, "filename": "Country.php", "line": "?"}}}, "count": 9, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\DashboardController@index", "uri": "GET dashboard", "controller": "App\\Http\\Controllers\\DashboardController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FLENOVO%20L340%2FDesktop%2Freact-starter-kit%2Fapp%2FHttp%2FControllers%2FDashboardController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/DashboardController.php:13-36</a>", "middleware": "web, auth, verified", "duration": "852ms", "peak_memory": "6MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1904560196 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1904560196\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-491561015 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-491561015\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-708466045 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik85TkdwcGFTYjc1cXBHL2dETnBnS1E9PSIsInZhbHVlIjoiYjlMZDhHVGcxaTcreExMeVVNZlFHbFRTdXZPZnhnUnZNTHV0ZnBYMjdmRjYwT2ZHVWc0QWlscG5BeGpHYjNPekZ5Sm1BTVdOOVVjMmJ5UWp3RlNZeTY5bHFIT1RtWlB4Zkd4VmhLdFA4T0t0VUF2YjN0SjUyc3JNYzkvWkc0Q2ciLCJtYWMiOiJiOWJlYzdiMTYyODUyMjJlNGFjMTBjOWU5MTZhMGQ5MzgzZTViZDUwYTZhNGRjNjI4M2VkNmZmMDg4NDA2OWZjIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia-version</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">70490311fe7c84acda8886406a6d884b</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://127.0.0.1:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">fr-FR,fr;q=0.9,ar-TN;q=0.8,ar;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1603 characters\">_cc_cookie={&quot;categories&quot;:[&quot;necessary&quot;],&quot;level&quot;:[&quot;necessary&quot;],&quot;revision&quot;:0,&quot;data&quot;:null,&quot;rfc_cookie&quot;:false,&quot;consent_date&quot;:&quot;2025-02-09T00:01:04.160Z&quot;,&quot;consent_uuid&quot;:&quot;410c5564-6d38-4cde-b65a-95ec6886d6c1&quot;,&quot;last_consent_update&quot;:&quot;2025-02-09T00:01:04.160Z&quot;}; locale=en; appearance=light; device_uuid=eyJpdiI6IkducU5Ec2dRdUU2RldQVXpEZjBOaWc9PSIsInZhbHVlIjoiR1hpL2FPTldjeDhodmY1MC92aXV5WXFpeGNiQXpBRWV2VVRkK0IyeHJWSTBoVHF0Nkc4Tjc0VXNWa3BSMHNHWElXa2NrSUd6QjhZSzdnTkpkaEQ0WmtGWWppVGthN293K1N2dHhDZnVkU1ZsNlQ2QXV4VnE0K29COXBnVWQwTktwU3pBb25TeFdIN0l4TWZXbDlUaDdCSndkck5TUW02blptaVVBbXpweVBLOHJFNks3VUxpMU82MkpjenVuUGFPIiwibWFjIjoiZDUwYjkyOWU2OTBiZDhjZGZiOWJhN2ZjM2M4ZGFhMWM0OTcxY2VkMDhkNjlkMWZkMjU3Yzk5MGM1Mzk4ZWFiNyIsInRhZyI6IiJ9; admin-mode=dark; admin-colorPref=dark; direction=false; _ga=GA1.1.1790330909.1748982694; _ga_699NE13B0K=GS2.1.s1749281199$o2$g0$t1749281199$j60$l0$h0; sidebar_state=true; XSRF-TOKEN=eyJpdiI6IjZQSTNYL1RadVdYNVY0M1Y4cFpnQnc9PSIsInZhbHVlIjoibkhPSUZqY3J3S2k3dGZKYitpMVU2anpOMUdMOUkzWWxpak5jNUx6NUZ5SHdUS2RzZkl3VC92ZDZCM293M1VZT3lYY2xnOGMycXlnVnUxOHgvbmZ4Zk5ldkUrNk5sWlVwMmxhYXVBZUVac0dhSVUwSlBxSm5BUzBjV3luYmpiaXIiLCJtYWMiOiJmYmM5ZDUyMDY1YTAzZWRlOTFiNWFhMWZhMmE0N2VjNTVhYzA3MGVlNTRlMzBjYzk0NzgwNmMzYzY3NDJjOTk0IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlBpbi9oTG5XSWNsSm83WVk2cStvdGc9PSIsInZhbHVlIjoiYWo2MGV4WlFHMDc0dW1GaFlxanhFK2xsS1RVWU5md3ZhQVM3eTdGUGtueXZYb3I5c1NHcUtnQ3VPTHVYUUVUWDVuOFpYd3I4L3h5OU15NzJCWnkyc2FSWktIMGNmejY5UzM0VXZVWVR2V1dMcDNUU1ByZFNEMFJFbTVVbCtDUm4iLCJtYWMiOiI3M2Y4MDllOTg3YWE2NTcwZTM2ZjZiNjBiOWMyNDNjYjc3YzBjMzNjMmM4MjAyMjU4N2I4NDcwZGU0YjBmZTUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708466045\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1041226669 data-indent-pad=\"  \"><span class=sf-dump-note>array:12</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_cc_cookie</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>locale</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>appearance</span>\" => \"<span class=sf-dump-str title=\"5 characters\">light</span>\"\n  \"<span class=sf-dump-key>device_uuid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-mode</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>admin-colorPref</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>direction</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_699NE13B0K</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>sidebar_state</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YTW8ck95BwzrmhjWZj1miCF8jzXN8EKMr44lYEKa</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">fkYN1wqLjl45zY3fDczAVdqwKjktRo550uiBDKyv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1041226669\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-905272573 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>x-inertia</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 18:07:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905272573\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-631745952 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">YTW8ck95BwzrmhjWZj1miCF8jzXN8EKMr44lYEKa</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JXN7JXKS3VHSTZWRQ8ACSZX5</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>app_settings</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>company</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Acme Corporation</span>\"\n      \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n      \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n      \"<span class=sf-dump-key>website</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>localization</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>default_currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>default_country</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>default_language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n      \"<span class=sf-dump-key>timezone</span>\" => \"<span class=sf-dump-str title=\"3 characters\">UTC</span>\"\n      \"<span class=sf-dump-key>date_format</span>\" => \"<span class=sf-dump-str title=\"5 characters\">d-m-Y</span>\"\n      \"<span class=sf-dump-key>time_format</span>\" => \"<span class=sf-dump-str title=\"2 characters\">12</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>ui</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>datatable_row_limit</span>\" => <span class=sf-dump-num>10</span>\n      \"<span class=sf-dump-key>employees_can_export_data</span>\" => <span class=sf-dump-const>true</span>\n    </samp>]\n    \"<span class=sf-dump-key>currency</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n      \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n      \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n      \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n      \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n      \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n      \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n    </samp>]\n    \"<span class=sf-dump-key>country</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>iso2</span>\" => \"<span class=sf-dump-str title=\"2 characters\">TN</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tunisia</span>\"\n      \"<span class=sf-dump-key>phonecode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">216</span>\"\n      \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n      \"<span class=sf-dump-key>currency_symbol</span>\" => \"<span class=sf-dump-str title=\"3 characters\">&#1578;.&#1583;</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>currencies</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">TND</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">tunisan dinar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str title=\"2 characters\">DT</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"5 characters\">right</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>true</span>\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GBP</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">British Pound</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#163;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">EUR</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Euro</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#8364;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">JPY</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Japanese Yen</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>&#165;</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n        \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n        \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"9 characters\">US Dollar</span>\"\n        \"<span class=sf-dump-key>symbol</span>\" => \"<span class=sf-dump-str>$</span>\"\n        \"<span class=sf-dump-key>position</span>\" => \"<span class=sf-dump-str title=\"4 characters\">left</span>\"\n        \"<span class=sf-dump-key>thousand_separator</span>\" => \"<span class=sf-dump-str>,</span>\"\n        \"<span class=sf-dump-key>decimal_separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n        \"<span class=sf-dump-key>decimals</span>\" => <span class=sf-dump-num>2</span>\n        \"<span class=sf-dump-key>is_default</span>\" => <span class=sf-dump-const>false</span>\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-631745952\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://127.0.0.1:8000/dashboard", "action_name": "dashboard", "controller_action": "App\\Http\\Controllers\\DashboardController@index"}, "badge": null}}