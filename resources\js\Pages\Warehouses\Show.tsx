import { Head } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Link } from '@inertiajs/react';
import { Edit, ArrowLeft } from 'lucide-react';

interface Warehouse {
    id: number;
    name: string;
    phone: string | null;
    email: string | null;
    address: string | null;
    is_active: boolean;
    is_default: boolean;
    created_at: string;
    updated_at: string;
}

interface Props {
    warehouse: Warehouse;
}

export default function Show({ warehouse }: Props) {
    return (
        <AppSidebarLayout>
            <Head title={`Warehouse - ${warehouse.name}`} />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => window.history.back()}
                        >
                            <ArrowLeft className="h-4 w-4" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-semibold">{warehouse.name}</h1>
                            <p className="text-sm text-muted-foreground">Warehouse Details</p>
                        </div>
                    </div>
                    <Button asChild>
                        <Link href={route('warehouses.edit', warehouse.id)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit Warehouse
                        </Link>
                    </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Basic Information</CardTitle>
                            <CardDescription>
                                Warehouse contact and location details
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Name</h3>
                                <p className="mt-1">{warehouse.name}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Phone</h3>
                                <p className="mt-1">{warehouse.phone || '-'}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
                                <p className="mt-1">{warehouse.email || '-'}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Address</h3>
                                <p className="mt-1 whitespace-pre-line">{warehouse.address || '-'}</p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Status Information</CardTitle>
                            <CardDescription>
                                Warehouse operational status
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Active Status</h3>
                                <p className="mt-1">
                                    <Badge variant={warehouse.is_active ? "default" : "secondary"}>
                                        {warehouse.is_active ? "Active" : "Inactive"}
                                    </Badge>
                                </p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Default Status</h3>
                                <p className="mt-1">
                                    <Badge variant={warehouse.is_default ? "default" : "secondary"}>
                                        {warehouse.is_default ? "Default" : "Not Default"}
                                    </Badge>
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>System Information</CardTitle>
                            <CardDescription>
                                Warehouse system details
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Created At</h3>
                                <p className="mt-1">{new Date(warehouse.created_at).toLocaleString()}</p>
                            </div>
                            
                            <div>
                                <h3 className="text-sm font-medium text-muted-foreground">Last Updated</h3>
                                <p className="mt-1">{new Date(warehouse.updated_at).toLocaleString()}</p>
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
        </AppSidebarLayout>
    );
} 