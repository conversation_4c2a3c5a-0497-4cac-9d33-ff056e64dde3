#!/bin/bash

# Restore Geographic Data Script
# Usage: ./scripts/restore-geo-data.sh

echo "🔄 Restoring geographic data..."

# Get database connection details from .env
DB_HOST=$(grep DB_HOST .env | cut -d '=' -f2)
DB_PORT=$(grep DB_PORT .env | cut -d '=' -f2)
DB_DATABASE=$(grep DB_DATABASE .env | cut -d '=' -f2)
DB_USERNAME=$(grep DB_USERNAME .env | cut -d '=' -f2)
DB_PASSWORD=$(grep DB_PASSWORD .env | cut -d '=' -f2)

# Check if backup files exist
if [ ! -f "storage/backups/countries.sql" ]; then
    echo "❌ Countries backup file not found!"
    exit 1
fi

if [ ! -f "storage/backups/states.sql" ]; then
    echo "❌ States backup file not found!"
    exit 1
fi

if [ ! -f "storage/backups/cities.sql" ]; then
    echo "❌ Cities backup file not found!"
    exit 1
fi

# Restore countries, states, and cities tables
echo "🔄 Restoring countries table..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -f storage/backups/countries.sql

echo "🔄 Restoring states table..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -f storage/backups/states.sql

echo "🔄 Restoring cities table..."
psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE -f storage/backups/cities.sql

echo "✅ Geographic data restoration completed!"
