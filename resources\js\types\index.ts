export interface Permission {
    id: number;
    name: string;
    module: string;
}

export interface Role {
    id: number;
    name: string;
    description: string | null;
    company_id: number | null;
    is_system: boolean;
    permissions: Permission[];
    created_at: string;
    updated_at: string;
}

export interface User {
    id: number;
    name: string;
    email: string;
    company_id: number;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    roles?: Role[];
}

export interface Company {
    id: number;
    name: string;
    email: string;
    phone: string;
    address: string;
    city_id?: number;
    state_id?: number;
    country_id?: string;
    created_at: string;
    updated_at: string;
    city?: City;
    state?: State;
    country?: Country;
}

export interface BreadcrumbItem {
    title: string;
    href?: string;
}

export interface NavItem {
    title: string;
    href: string;
    icon: any;
    permission?: string;
    current?: boolean;
}

export interface PageProps {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            company_id: number;
        };
    };
}

export interface Country {
    id?: number;
    country: string;
    iso2: string;
    name?: string;
    phonecode?: string;
}

export interface State {
    id: number;
    name: string;
    code: string;
    country_code: string;
}

export interface City {
    id: number;
    name: string;
    state_id: number;
}

export interface Customer {
    id: number;
    full_name: string;
    email: string;
    primary_phone: string;
    primary_phonecode: string;
    secondary_phone: string;
    secondary_phonecode: string;
    address: string;
    city_id: number;
    state_id: number;
    country_id: string;
    postal_code: string;
    gender: string;
    company_id: number;
    created_at: string;
    updated_at: string;
    city?: City;
    state?: State;
    country?: Country;
} 