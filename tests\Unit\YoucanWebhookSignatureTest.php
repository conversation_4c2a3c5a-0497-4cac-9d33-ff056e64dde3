<?php

namespace Tests\Unit;

use Tests\TestCase;

class YoucanWebhookSignatureTest extends TestCase
{
    public function test_webhook_signature_verification()
    {
        $payload = [
            'event' => 'order.create',
            'store_id' => 'test-store-123',
            'data' => [
                'id' => 'order-123',
                'total' => 100.00,
            ],
        ];

        $secret = 'test-secret-key';
        $expectedSignature = hash_hmac('sha256', json_encode($payload), $secret);

        // Test that the signature calculation works correctly
        $this->assertNotEmpty($expectedSignature);
        $this->assertEquals(64, strlen($expectedSignature)); // SHA256 produces 64 character hex string
        
        // Test that the same payload produces the same signature
        $secondSignature = hash_hmac('sha256', json_encode($payload), $secret);
        $this->assertEquals($expectedSignature, $secondSignature);
        
        // Test that different payload produces different signature
        $differentPayload = $payload;
        $differentPayload['data']['id'] = 'order-456';
        $differentSignature = hash_hmac('sha256', json_encode($differentPayload), $secret);
        $this->assertNotEquals($expectedSignature, $differentSignature);
        
        // Test that different secret produces different signature
        $differentSecret = 'different-secret';
        $differentSecretSignature = hash_hmac('sha256', json_encode($payload), $differentSecret);
        $this->assertNotEquals($expectedSignature, $differentSecretSignature);
    }

    public function test_hash_equals_prevents_timing_attacks()
    {
        $correctSignature = 'abc123def456';
        $wrongSignature = 'xyz789uvw012';
        
        // hash_equals should return false for different strings
        $this->assertFalse(hash_equals($correctSignature, $wrongSignature));
        
        // hash_equals should return true for identical strings
        $this->assertTrue(hash_equals($correctSignature, $correctSignature));
        
        // hash_equals should handle different lengths safely
        $this->assertFalse(hash_equals($correctSignature, 'short'));
        $this->assertFalse(hash_equals('short', $correctSignature));
    }
}
