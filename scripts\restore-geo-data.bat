@echo off
REM Restore Geographic Data Script for Windows
REM Usage: scripts\restore-geo-data.bat

echo 🔄 Restoring geographic data...

REM Read database connection details from .env
for /f "tokens=2 delims==" %%a in ('findstr "DB_HOST" .env') do set DB_HOST=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PORT" .env') do set DB_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_DATABASE" .env') do set DB_DATABASE=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_USERNAME" .env') do set DB_USERNAME=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PASSWORD" .env') do set DB_PASSWORD=%%a

REM Check if backup files exist
if not exist "storage\backups\countries.sql" (
    echo ❌ Countries backup file not found!
    pause
    exit /b 1
)

if not exist "storage\backups\states.sql" (
    echo ❌ States backup file not found!
    pause
    exit /b 1
)

if not exist "storage\backups\cities.sql" (
    echo ❌ Cities backup file not found!
    pause
    exit /b 1
)

REM Set PGPASSWORD environment variable
set PGPASSWORD=%DB_PASSWORD%

REM Restore countries, states, and cities tables
echo 🔄 Restoring countries table...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -f storage\backups\countries.sql

echo 🔄 Restoring states table...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -f storage\backups\states.sql

echo 🔄 Restoring cities table...
psql -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -f storage\backups\cities.sql

echo ✅ Geographic data restoration completed!

REM Clear password from environment
set PGPASSWORD=

pause
