import { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';

interface Supplier {
    id: number;
    name: string;
    code: string;
    contact_person: string | null;
    phone: string | null;
    email: string | null;
    address: string | null;
    status: 'active' | 'inactive';
}

interface Props {
    supplier: Supplier;
    errors?: Record<string, string>;
}

type FormData = {
    name: string;
    code: string;
    contact_person: string;
    phone: string;
    email: string;
    address: string;
    status: 'active' | 'inactive';
}

export default function Edit({ supplier, errors }: Props) {
    const form = useForm<FormData>({
        name: supplier.name,
        code: supplier.code,
        contact_person: supplier.contact_person || '',
        phone: supplier.phone || '',
        email: supplier.email || '',
        address: supplier.address || '',
        status: supplier.status,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.put(route('suppliers.update', supplier.id));
    };

    return (
        <AppSidebarLayout>
            <Head title={`Edit Supplier: ${supplier.name}`} />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-semibold">Edit Supplier</h1>
                        <p className="text-sm text-muted-foreground">Update supplier information</p>
                    </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Supplier Information</CardTitle>
                            <CardDescription>
                                Update the supplier's basic information
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Full Name</Label>
                                    <Input
                                        id="name"
                                        placeholder="Enter supplier name"
                                        value={form.data.name}
                                        onChange={e => form.setData('name', e.target.value)}
                                    />
                                    {errors?.name && (
                                        <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <Label htmlFor="code">Supplier Code</Label>
                                    <Input
                                        id="code"
                                        placeholder="Enter supplier code"
                                        value={form.data.code}
                                        onChange={e => form.setData('code', e.target.value)}
                                    />
                                    {errors?.code && (
                                        <p className="text-red-500 text-sm mt-1">{errors.code}</p>
                                    )}
                                </div>
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="contact_person">Contact Person</Label>
                                    <Input
                                        id="contact_person"
                                        placeholder="Enter contact person name"
                                        value={form.data.contact_person}
                                        onChange={e => form.setData('contact_person', e.target.value)}
                                    />
                                    {errors?.contact_person && (
                                        <p className="text-red-500 text-sm mt-1">{errors.contact_person}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        placeholder="Enter phone number"
                                        value={form.data.phone}
                                        onChange={e => form.setData('phone', e.target.value)}
                                    />
                                    {errors?.phone && (
                                        <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                                    )}
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Enter email address"
                                    value={form.data.email}
                                    onChange={e => form.setData('email', e.target.value)}
                                />
                                {errors?.email && (
                                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                                )}
                            </div>
                            
                            <div>
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    placeholder="Enter supplier address"
                                    value={form.data.address}
                                    onChange={e => form.setData('address', e.target.value)}
                                />
                                {errors?.address && (
                                    <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                                )}
                            </div>
                            
                            <div>
                                <Label htmlFor="status">Status</Label>
                                <Select
                                    value={form.data.status}
                                    onValueChange={(value) => form.setData('status', value as 'active' | 'inactive')}
                                >
                                    <SelectTrigger id="status">
                                        <SelectValue placeholder="Select status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="active">Active</SelectItem>
                                        <SelectItem value="inactive">Inactive</SelectItem>
                                    </SelectContent>
                                </Select>
                                {errors?.status && (
                                    <p className="text-red-500 text-sm mt-1">{errors.status}</p>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                    
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" onClick={() => window.history.back()}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={form.processing}>
                            {form.processing ? 'Updating...' : 'Update Supplier'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppSidebarLayout>
    );
} 