<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Inertia\Inertia;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Http;
use App\Models\CompanyCmsIntegration;
use App\Models\CmsPlatform;
use App\Models\Product;
use App\Models\ProductVariant;
use App\Models\Customer;
use App\Services\CmsIntegration\YoucanCmsService;
use App\Jobs\SyncYoucanOrders;

class YouCanController extends Controller
{
    /**
     * Show the YouCan import form
     */
    public function showImport()
    {
        $companyId = auth()->user()->company_id;
        
        // Get YouCan platform
        $platform = CmsPlatform::bySlug('youcan')->first();
        
        // Check if YouCan integration exists for this company
        $integration = null;
        $hasCredentials = false;
        
        if ($platform) {
            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();
            
            $hasCredentials = $integration && $integration->is_active;
        }

        // Get stores from the integration if available
        $youcanStores = [];
        $currentStoreId = null;

        if ($integration) {
            // Try to sync stores from API first
            try {
                $youcanService = new YoucanCmsService($integration);
                $syncResult = $youcanService->syncStores();

                // Refresh the integration to get updated stores
                $integration->refresh();
                $youcanStores = $integration->stores->toArray();

                // Get current store ID from service
                $currentStoreId = $youcanService->getCurrentStoreId();

                // If no current store is set, use the first available store
                if (!$currentStoreId && !empty($youcanStores)) {
                    $currentStoreId = $youcanStores[0]['store_id'];
                }
            } catch (\Exception $e) {
                // Fall back to database stores if API sync fails
                $youcanStores = $integration->stores->toArray();
                $currentStoreId = $integration->stores->where('is_default', true)->first()?->store_id
                    ?? $integration->stores->first()?->store_id;

                Log::warning('Failed to sync YouCan stores, using cached data', [
                    'error' => $e->getMessage(),
                    'integration_id' => $integration->id,
                ]);
            }
        }

        return Inertia::render('Orders/YoucanImport', [
            'youcanConnected' => $hasCredentials,
            'youcanStores' => $youcanStores,
            'currentStoreId' => $currentStoreId,
            'youcanToken' => null, // We'll handle authentication through the service
            'integration' => $integration,
        ]);
    }

    /**
     * Test YouCan API connection
     */
    public function testConnection()
    {
        try {
            $companyId = auth()->user()->company_id;
            
            // Get YouCan platform
            $platform = CmsPlatform::bySlug('youcan')->first();
            
            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            // Get integration
            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Test connection using the CMS service
            $youcanService = new YoucanCmsService($integration);
            $result = $youcanService->testConnection();

            if ($result) {
                return response()->json([
                    'success' => true,
                    'message' => 'Successfully connected to YouCan API'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to connect to YouCan API'
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('YouCan connection test failed: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to connect to YouCan API: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test import with a few recent orders for debugging
     */
    public function testImport(Request $request)
    {
        try {
            $companyId = auth()->user()->company_id;

            Log::info('Starting YouCan test import', [
                'user_id' => auth()->id(),
                'company_id' => $companyId
            ]);

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();

            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Test connection
            try {
                $youcanService = new YoucanCmsService($integration);
                $testResult = $youcanService->testConnection();

                if (!$testResult) {
                    throw new \Exception('Connection test failed');
                }
            } catch (\Exception $e) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to connect to YouCan API: ' . $e->getMessage()
                ], 400);
            }

            // Get just 3 recent orders for testing
            $params = [
                'page' => 1,
                'limit' => 3
            ];

            // Dispatch the sync job
            SyncYoucanOrders::dispatch(
                $companyId,
                $params,
                auth()->id()
            )->onQueue('youcan-sync');

            return response()->json([
                'success' => true,
                'message' => 'Test import initiated with 3 recent orders. Check the logs for detailed results.'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to initiate YouCan test import', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate test import: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import orders from YouCan
     */
    public function importOrders(Request $request)
    {
        try {
            $companyId = auth()->user()->company_id;
            
            Log::info('Starting YouCan import process', [
                'user_id' => auth()->id(),
                'company_id' => $companyId
            ]);

            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'limit' => 'nullable|integer|min:1|max:250', // Allow custom limit with reasonable bounds
            ]);

            Log::info('Validated import parameters', [
                'date_from' => $validated['date_from'] ?? null,
                'date_to' => $validated['date_to'] ?? null
            ]);

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();
            
            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Test connection before dispatching job
            try {
                $youcanService = new YoucanCmsService($integration);
                $testResult = $youcanService->testConnection();
                
                if (!$testResult) {
                    throw new \Exception('Connection test failed');
                }
                
                Log::info('YouCan connection test successful');
            } catch (\Exception $e) {
                Log::error('YouCan connection test failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to connect to YouCan API: ' . $e->getMessage()
                ], 400);
            }

            // Prepare parameters for the API call
            $params = array_filter([
                'created_at_min' => $validated['date_from'] ?? null,
                'created_at_max' => $validated['date_to'] ?? null,
                'page' => 1,
                'limit' => $validated['limit'] ?? 100  // Default 100, customizable for better performance
            ]);

            Log::info('Dispatching YouCan sync job', [
                'params' => $params
            ]);

            // Check if there are existing orders to provide better feedback
            $existingOrdersCount = \App\Models\Order::where('company_id', $companyId)
                ->where('source', 'youcan')
                ->count();

            // Dispatch the sync job
            SyncYoucanOrders::dispatch(
                $companyId,
                $params,
                auth()->id()
            )->onQueue('youcan-sync');

            $message = 'Order sync has been initiated. ';
            if ($existingOrdersCount > 0) {
                $message .= "You currently have {$existingOrdersCount} YouCan orders. ";
                $message .= 'If no new orders appear, it means all orders from the selected date range already exist in your database.';
            } else {
                $message .= 'You will be notified when it completes.';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (ValidationException $e) {
            Log::error('YouCan import validation failed', [
                'errors' => $e->errors()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            Log::error('Failed to initiate YouCan order sync', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to initiate order sync: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync products from YouCan
     */
    public function syncProducts()
    {
        try {
            $companyId = auth()->user()->company_id;
            
            Log::info('Starting YouCan products sync', [
                'user_id' => auth()->id(),
                'company_id' => $companyId
            ]);

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();
            
            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Get products from YouCan using CMS service
            $youcanService = new YoucanCmsService($integration);
            $products = $youcanService->getProducts();

            if (empty($products['data'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No products found to sync'
                ]);
            }

            $syncedCount = 0;
            $updatedCount = 0;

            foreach ($products['data'] as $productData) {
                try {
                    // Check if product exists by code
                    $product = Product::where('company_id', auth()->user()->company_id)
                        ->where('code', $productData['id'])
                        ->first();

                    $productDataToSave = [
                        'name' => $productData['name'],
                        'code' => $productData['id'],
                        'product_type' => 'standard',
                        'sell_price' => $productData['price'] ?? 0,
                        'negotiation_price' => $productData['price'] ?? 0,
                        'total_qty' => $productData['quantity'] ?? 0,
                        'alert_quantity' => 0,
                        'status' => 'active',
                        'company_id' => auth()->user()->company_id,
                        'product_details' => $productData['description'] ?? null,
                    ];

                    if ($product) {
                        // Update existing product
                        $product->update($productDataToSave);
                        $updatedCount++;
                    } else {
                        // Create new product
                        $product = Product::create($productDataToSave);
                        $syncedCount++;
                    }

                    // Handle variants if they exist
                    if (!empty($productData['variants'])) {
                        foreach ($productData['variants'] as $variantData) {
                            $variant = ProductVariant::updateOrCreate(
                                [
                                    'product_id' => $product->id,
                                    'sku' => $variantData['id'],
                                    'company_id' => auth()->user()->company_id,
                                ],
                                [
                                    'variant_combination' => $variantData['options'] ?? [],
                                    'additional_purchase_price' => 0,
                                    'additional_sell_price' => $variantData['price'] ?? 0,
                                    'additional_negotiation_price' => $variantData['price'] ?? 0,
                                    'total_qty' => $variantData['quantity'] ?? 0,
                                    'status' => 'active',
                                ]
                            );
                        }
                    }

                } catch (\Exception $e) {
                    Log::error('Error processing product: ' . $e->getMessage(), [
                        'product_id' => $productData['id'] ?? 'unknown',
                        'trace' => $e->getTraceAsString()
                    ]);
                    continue;
                }
            }

            return response()->json([
                'success' => true,
                'message' => "Successfully synced products. Created: {$syncedCount}, Updated: {$updatedCount}"
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to sync YouCan products', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send products to YouCan store
     */
    public function sendProducts(Request $request)
    {
        try {
            $request->validate([
                'product_ids' => 'required|array',
                'product_ids.*' => 'exists:products,id',
                'integration_id' => 'required|exists:company_cms_integrations,id',
                'store_id' => 'required|string',
            ]);

            $companyId = auth()->user()->company_id;
            $productIds = $request->product_ids;
            $integrationId = $request->integration_id;
            $storeId = $request->store_id;

            Log::info('Starting YouCan products send', [
                'user_id' => auth()->id(),
                'company_id' => $companyId,
                'product_ids' => $productIds,
                'integration_id' => $integrationId,
                'store_id' => $storeId,
            ]);

            // Get the integration
            $integration = CompanyCmsIntegration::with(['cmsPlatform', 'stores'])
                ->where('id', $integrationId)
                ->where('company_id', $companyId)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'Integration not found or access denied.'
                ], 404);
            }

            // Verify the store belongs to this integration
            $store = $integration->stores()->where('store_id', $storeId)->first();
            if (!$store) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found in this integration.'
                ], 404);
            }

            // Initialize YouCan service
            $youcanService = new YoucanCmsService($integration);

            // Switch to the selected store
            $youcanService->switchStore($storeId);

            // Get products with their variants
            $products = Product::where('company_id', $companyId)
                ->whereIn('id', $productIds)
                ->with(['variants'])
                ->get();

            if ($products->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No products found to send'
                ]);
            }

            $sentCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($products as $product) {
                try {
                    // Prepare product data
                    $productData = [
                        'name' => $product->name,
                        'code' => $product->code,
                        'product_details' => $product->product_details,
                        'sell_price' => $product->sell_price,
                        'negotiation_price' => $product->negotiation_price,
                        'purchase_price' => $product->purchase_price,
                        'total_qty' => $product->total_qty,
                        'status' => $product->status,
                        'image_url' => $product->file_url,
                    ];

                    // Add variants if they exist
                    if ($product->variants->isNotEmpty()) {
                        $productData['variants'] = $product->variants->map(function ($variant) {
                            return [
                                'sku' => $variant->sku,
                                'additional_sell_price' => $variant->additional_sell_price,
                                'additional_negotiation_price' => $variant->additional_negotiation_price,
                                'additional_purchase_price' => $variant->additional_purchase_price,
                                'total_qty' => $variant->total_qty,
                                'variant_combination' => $variant->variant_combination,
                            ];
                        })->toArray();
                    }

                    // Send product to YouCan
                    $response = $youcanService->createProduct($productData);

                    // Update product with YouCan details
                    $product->update([
                        'youcan_product_id' => $response['id'] ?? null,
                        'youcan_store_id' => $storeId,
                        'youcan_synced_at' => now(),
                    ]);

                    $sentCount++;

                    Log::info('Product sent to YouCan successfully', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'youcan_product_id' => $response['id'] ?? 'Unknown',
                        'youcan_store_id' => $storeId,
                    ]);

                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "Product '{$product->name}': " . $e->getMessage();

                    Log::error('Failed to send product to YouCan', [
                        'product_id' => $product->id,
                        'product_name' => $product->name,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $message = "Successfully sent {$sentCount} products to YouCan.";
            if ($failedCount > 0) {
                $message .= " {$failedCount} products failed.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            Log::error('YouCan products send failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send products: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send customers to YouCan store
     */
    public function sendCustomers(Request $request)
    {
        try {
            $request->validate([
                'customer_ids' => 'required|array',
                'customer_ids.*' => 'exists:customers,id',
                'integration_id' => 'required|exists:company_cms_integrations,id',
                'store_id' => 'required|string',
            ]);

            $companyId = auth()->user()->company_id;
            $customerIds = $request->customer_ids;
            $integrationId = $request->integration_id;
            $storeId = $request->store_id;

            Log::info('Starting YouCan customers send', [
                'user_id' => auth()->id(),
                'company_id' => $companyId,
                'customer_ids' => $customerIds,
                'integration_id' => $integrationId,
                'store_id' => $storeId,
            ]);

            // Get the integration
            $integration = CompanyCmsIntegration::with(['cmsPlatform', 'stores'])
                ->where('id', $integrationId)
                ->where('company_id', $companyId)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'Integration not found or access denied.'
                ], 404);
            }

            // Verify the store belongs to this integration
            $store = $integration->stores()->where('store_id', $storeId)->first();
            if (!$store) {
                return response()->json([
                    'success' => false,
                    'message' => 'Store not found in this integration.'
                ], 404);
            }

            // Initialize YouCan service
            $youcanService = new YoucanCmsService($integration);

            // Switch to the selected store
            $youcanService->switchStore($storeId);

            // Get customers
            $customers = Customer::where('company_id', $companyId)
                ->whereIn('id', $customerIds)
                ->with(['country', 'state', 'city'])
                ->get();

            if ($customers->isEmpty()) {
                return response()->json([
                    'success' => false,
                    'message' => 'No customers found to send'
                ]);
            }

            $sentCount = 0;
            $failedCount = 0;
            $errors = [];

            foreach ($customers as $customer) {
                try {
                    // Prepare customer data
                    $customerData = [
                        'full_name' => $customer->full_name,
                        'email' => $customer->email,
                        'primary_phone' => $customer->primary_phone,
                        'address' => $customer->address,
                        'city' => $customer->city->name ?? '',
                        'state' => $customer->state->name ?? '',
                        'country' => $customer->country->name ?? '',
                        'postal_code' => $customer->postal_code,
                    ];

                    // Send customer to YouCan
                    $response = $youcanService->createCustomer($customerData);

                    // Update customer with YouCan details
                    $customer->update([
                        'youcan_customer_id' => $response['id'] ?? null,
                        'youcan_store_id' => $storeId,
                        'youcan_synced_at' => now(),
                    ]);

                    $sentCount++;

                    Log::info('Customer sent to YouCan successfully', [
                        'customer_id' => $customer->id,
                        'customer_name' => $customer->full_name,
                        'youcan_customer_id' => $response['id'] ?? 'Unknown',
                        'youcan_store_id' => $storeId,
                    ]);

                } catch (\Exception $e) {
                    $failedCount++;
                    $errors[] = "Customer '{$customer->full_name}': " . $e->getMessage();

                    Log::error('Failed to send customer to YouCan', [
                        'customer_id' => $customer->id,
                        'customer_name' => $customer->full_name,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $message = "Successfully sent {$sentCount} customers to YouCan.";
            if ($failedCount > 0) {
                $message .= " {$failedCount} customers failed.";
            }

            return response()->json([
                'success' => true,
                'message' => $message,
                'sent_count' => $sentCount,
                'failed_count' => $failedCount,
                'errors' => $errors,
            ]);

        } catch (\Exception $e) {
            Log::error('YouCan customers send failed: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to send customers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Import customers from YouCan
     */
    public function syncCustomers()
    {
        try {
            $companyId = auth()->user()->company_id;

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();

            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Get customers from YouCan using CMS service
            $youcanService = new YoucanCmsService($integration);
            $customers = $youcanService->getCustomers();

            if (empty($customers['data'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'No customers found to sync'
                ]);
            }

            $synced = 0;
            $updated = 0;

            foreach ($customers['data'] as $customerData) {
                // Adjust these fields as needed for your Customer model
                $customer = Customer::updateOrCreate(
                    [
                        'company_id' => auth()->user()->company_id,
                        'email' => $customerData['email'] ?? null,
                        'primary_phone' => $customerData['phone'] ?? null,
                    ],
                    [
                        'full_name' => $customerData['full_name'] ?? 'Unknown',
                        'address' => $customerData['location'] ?? null,
                        // Add other fields as needed
                    ]
                );
                $customer->wasRecentlyCreated ? $synced++ : $updated++;
            }

            return response()->json([
                'success' => true,
                'message' => "Customers synced. Created: {$synced}, Updated: {$updated}"
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to sync YouCan customers: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'Failed to sync customers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Switch YouCan store and update database
     */
    public function switchStore($storeId)
    {
        try {
            $companyId = auth()->user()->company_id;

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();

            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Use the YouCan service to switch stores
            $youcanService = new YoucanCmsService($integration);
            $result = $youcanService->switchStore($storeId);

            if ($result['success']) {
                // Update the default store in the database
                $integration->stores()->update(['is_default' => false]);
                $integration->stores()->where('store_id', $storeId)->update(['is_default' => true]);

                return response()->json([
                    'success' => true,
                    'message' => 'Store switched successfully',
                    'store_id' => $storeId
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to switch store'
                ], 400);
            }
        } catch (\Exception $e) {
            Log::error('Failed to switch YouCan store', [
                'store_id' => $storeId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to switch store: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get stores list from YouCan API and sync to database
     */
    public function syncStores()
    {
        try {
            $companyId = auth()->user()->company_id;

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();

            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            // Use the YouCan service to sync stores
            $youcanService = new YoucanCmsService($integration);
            $result = $youcanService->syncStores();

            return response()->json([
                'success' => true,
                'message' => "Stores synced successfully. Created: {$result['created']}, Updated: {$result['updated']}",
                'stores' => collect($result['stores'])->toArray(),
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to sync YouCan stores', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync stores: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get current store information
     */
    public function getCurrentStore()
    {
        try {
            $companyId = auth()->user()->company_id;

            // Get YouCan platform and integration
            $platform = CmsPlatform::bySlug('youcan')->first();

            if (!$platform) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan platform not found'
                ], 404);
            }

            $integration = CompanyCmsIntegration::forCompany($companyId)
                ->forPlatform($platform->id)
                ->first();

            if (!$integration) {
                return response()->json([
                    'success' => false,
                    'message' => 'YouCan integration not found. Please configure it first.'
                ], 404);
            }

            $youcanService = new YoucanCmsService($integration);
            $currentStoreId = $youcanService->getCurrentStoreId();

            // Get all stores for this integration
            $stores = $integration->stores()->get();

            // If no current store is set, set the first active store as default
            if (!$currentStoreId && $stores->isNotEmpty()) {
                $firstStore = $stores->where('is_active', true)->first();
                if ($firstStore) {
                    $currentStoreId = $firstStore->store_id;

                    // Update integration settings
                    $settings = $integration->settings ?? [];
                    $settings['current_store_id'] = $currentStoreId;
                    $integration->update(['settings' => $settings]);

                    // Update store default status
                    $stores->each(function ($store) use ($currentStoreId) {
                        $store->update(['is_default' => $store->store_id === $currentStoreId]);
                    });
                }
            }

            $currentStore = $stores->where('store_id', $currentStoreId)->first();

            return response()->json([
                'success' => true,
                'current_store' => $currentStore,
                'current_store_id' => $currentStoreId,
                'stores' => $stores->map(function ($store) use ($currentStoreId) {
                    return [
                        'id' => $store->store_id,
                        'name' => $store->store_name,
                        'url' => $store->store_url,
                        'is_active' => $store->is_active,
                        'is_current' => $store->store_id === $currentStoreId,
                        'is_default' => $store->is_default,
                    ];
                })
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to get current YouCan store', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get current store: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * TEMP: Set a test YouCan session for UI testing
     */
    public function setSessionTest()
    {
        session([
            'youcan_token' => 'TEST_TOKEN',
            'youcan_stores' => [
                [
                    'store_id' => '2cc3ab31-868c-4e8f-b4fd-40ecb49e22c8',
                    'slug' => 'your_store',
                    'is_active' => true,
                    'is_email_verified' => false,
                ],
                [
                    'store_id' => '2cc3ab32-868c-4e8f-b4fd-40ecb49e22c8',
                    'slug' => 'your_store_2',
                    'is_active' => false,
                    'is_email_verified' => false,
                ],
            ],
            'youcan_current_store_id' => '2cc3ab31-868c-4e8f-b4fd-40ecb49e22c8',
        ]);
        return redirect()->route('youcan.import');
    }
}
