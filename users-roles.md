# Users & Roles Module

## Overview
The Users & Roles module manages company users and their permissions within the system. It provides functionality for creating, editing, and managing users and their associated roles.

## Features
- User management (CRUD operations)
- Role-based access control
- Permission management
- User profile settings

## Database Structure

### Users Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | User's full name |
| email | string | User's email address (unique) |
| email_verified_at | timestamp | When email was verified |
| password | string | Hashed password |
| company_id | bigint | Foreign key to companies table |
| role_id | bigint | Foreign key to roles table |
| remember_token | string | Token for "remember me" functionality |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Roles Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Role name |
| description | text | Role description |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Permissions Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Permission name |
| description | text | Permission description |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Role_Permission Table (Pivot)
| Column | Type | Description |
|--------|------|-------------|
| role_id | bigint | Foreign key to roles table |
| permission_id | bigint | Foreign key to permissions table |

## User Interface
- Users listing page with search and filtering
- User creation and editing forms
- Role management interface
- Permission assignment interface
- User profile settings page

## Business Rules
- Users can only access data within their company scope
- Only users with appropriate permissions can manage other users
- Each user must have at least one role
- Default roles include Admin, Manager, and Staff
- Passwords must meet minimum security requirements