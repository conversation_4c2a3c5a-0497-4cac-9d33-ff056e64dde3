# Suppliers Module

## Overview
The Suppliers module manages information about product suppliers, including contact details, supplied products, and order history.

## Features
- Supplier management (CRUD operations)
- Supplier search and filtering
- Supplier product catalog
- Supplier order history
- Supplier contact management

## Database Structure

### Suppliers Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Supplier name |
| contact_name | string | Primary contact person |
| email | string | Supplier email address |
| phone | string | Supplier phone number |
| address | text | Supplier address |
| city | string | Supplier city |
| state | string | Supplier state/province |
| postal_code | string | Supplier postal/zip code |
| country | string | Supplier country |
| website | string | Supplier website URL |
| tax_id | string | Tax identification number |
| payment_terms | string | Payment terms |
| notes | text | Supplier notes |
| is_active | boolean | Whether supplier is active |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Supplier_Orders Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| supplier_id | bigint | Foreign key to suppliers table |
| order_number | string | Purchase order number |
| order_date | date | Date order was placed |
| expected_delivery | date | Expected delivery date |
| status | string | Order status |
| total_amount | decimal | Total order amount |
| notes | text | Order notes |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Supplier_Order_Items Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| supplier_order_id | bigint | Foreign key to supplier_orders table |
| product_id | bigint | Foreign key to products table |
| quantity | integer | Quantity ordered |
| unit_price | decimal | Price per unit |
| total_price | decimal | Total price for this item |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

## User Interface
- Supplier listing page with search and filtering
- Supplier creation and editing forms
- Supplier details view
- Supplier order management interface
- Supplier product catalog view

## Business Rules
- Suppliers must have a name and at least one contact method
- Supplier email addresses should be unique within a company
- Products can be associated with multiple suppliers
- Supplier data is scoped to the company level
- Supplier orders track inventory replenishment