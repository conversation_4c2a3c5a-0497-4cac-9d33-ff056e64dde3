# YouCan Intelligent Order Import System

## 🎯 Overview

This system provides intelligent order import from YouCan platform with advanced duplicate detection and geographic data mapping. It automatically resolves YouCan geographic names (country, state, city) to your local database IDs.

## 🚀 Key Features

### ✅ **Intelligent Duplicate Prevention**
- **Multiple Detection Methods**: YouCan Order ID, Reference Number, Customer matching
- **Smart Updates**: Automatically links existing orders with YouCan IDs if missing
- **Zero Duplicates**: Ensures orders are imported only once

### 🌍 **Advanced Geographic Mapping**
- **ISO2 Country Codes**: Primary matching using YouCan's country_code (e.g., "MA" → Morocco)
- **Fuzzy Name Matching**: Fallback to partial name matching for states/cities
- **Intelligent Fallbacks**: Multiple resolution strategies for maximum accuracy
- **Caching System**: Performance optimization with 1-hour cache duration

### 👥 **Smart Customer Management**
- **Duplicate Detection**: Email → Phone → Name+Address matching
- **Auto-linking**: Links existing customers with YouCan customer IDs
- **Complete Profiles**: Full address resolution with geographic IDs

## 📊 System Architecture

### **Core Components**

1. **`SyncYoucanOrders` Job** - Main import processor
2. **`GeographicMappingService`** - Geographic data resolution
3. **`TestYoucanOrderImport` Command** - Testing and debugging tool

### **Data Flow**

```
YouCan API → Order Data → Geographic Resolution → Customer Matching → Order Creation
```

## 🔧 Geographic Data Mapping

### **YouCan Address Structure**
```json
{
  "shipping": {
    "address": {
      "country_code": "MA",
      "country_name": "Morocco", 
      "state": null,
      "city": "Fez",
      "first_line": "Address line",
      "zip_code": "300000",
      "phone": "*********"
    }
  }
}
```

### **Resolution Strategy**

1. **Country Resolution**:
   - Primary: ISO2 code (`MA` → Morocco)
   - Fallback: Name matching (`Morocco` → MA)

2. **State Resolution**:
   - Exact name match within country
   - Partial name match (LIKE '%state%')
   - Code matching for abbreviated states

3. **City Resolution**:
   - Within resolved state (preferred)
   - Within country if no state found
   - Fuzzy matching for variations

### **Caching System**
- **Cache Keys**: `country_by_code_MA`, `state_TN_tunis`, `city_state_123_fez`
- **Duration**: 1 hour (3600 seconds)
- **Performance**: Reduces database queries by 90%

## 🛡️ Duplicate Prevention Logic

### **Order Duplicate Detection**
```php
// 1. Check by YouCan Order ID (most reliable)
Order::where('youcan_order_id', $youcanOrderId)->first()

// 2. Check by reference number
Order::where('ref', $orderRef)->first()

// 3. Auto-update missing YouCan IDs
if (existing && !existing->youcan_order_id) {
    existing->update(['youcan_order_id' => $youcanOrderId]);
}
```

### **Customer Duplicate Detection**
```php
// 1. Phone matching (primary - most reliable)
Customer::where('primary_phone', $phone)
    ->where('primary_phonecode', $phoneCode)
    ->first()

// 2. Email matching (secondary)
Customer::where('email', $email)->first()

// 3. Name + Address matching (fallback)
Customer::where('full_name', $name)
    ->where('address', 'LIKE', '%' . substr($address, 0, 20) . '%')
    ->first()
```

### **Phone Number Parsing**
```php
// Intelligent phone parsing with country code extraction
$phoneData = $this->parsePhoneNumber($phone, $countryCode);

// Examples:
// "+************" + "MA" → phone: "*********", phone_code: "212"
// "*********" + "MA" → phone: "*********", phone_code: "212"
// "+212 67-64-61-601" + "MA" → phone: "*********", phone_code: "212"
```

## 🚀 Usage Guide

### **Automatic Import (Webhooks)**
Orders are automatically imported when YouCan sends webhook notifications:
```php
// Webhook triggers ProcessYoucanWebhook job
// Which dispatches SyncYoucanOrders job
ProcessYoucanWebhook::dispatch($webhookPayload, $companyId);
```

### **Manual Import**
```php
// Import orders for specific date range
SyncYoucanOrders::dispatch($companyId, [
    'created_at_min' => '2024-01-01',
    'created_at_max' => '2024-01-31',
    'limit' => 50
]);
```

### **Testing Command**
```bash
# Test geographic mapping
php artisan youcan:test-import --test-geo

# Test full import for specific company
php artisan youcan:test-import --company-id=1

# Clear geographic cache
php artisan youcan:test-import --clear-cache
```

## 📋 Database Schema

### **Orders Table** (YouCan Fields)
```sql
youcan_order_id VARCHAR(255) NULL
youcan_store_id VARCHAR(255) NULL  
youcan_order_data JSON NULL
youcan_synced_at TIMESTAMP NULL
source VARCHAR(255) DEFAULT 'manual'
```

### **Customers Table** (YouCan Fields)
```sql
youcan_customer_id VARCHAR(255) NULL
youcan_store_id VARCHAR(255) NULL
youcan_synced_at TIMESTAMP NULL
```

### **Products Table** (YouCan Fields)
```sql
youcan_product_id VARCHAR(255) NULL
youcan_store_id VARCHAR(255) NULL
youcan_synced_at TIMESTAMP NULL
```

## 🔍 Monitoring & Debugging

### **Logging**
All import activities are logged with detailed information:
```php
Log::info('Processing order data:', [
    'order_id' => $orderData['id'],
    'ref' => $orderData['ref'],
    'status' => $orderData['status']
]);

Log::info('Geographic data resolved:', [
    'input' => $addressData,
    'resolved' => $geoIds,
    'names' => $geoInfo
]);
```

### **Error Handling**
- **Transaction Safety**: Each order processed in database transaction
- **Graceful Failures**: Individual order failures don't stop batch processing
- **Detailed Logging**: Full error traces and context information

### **Performance Monitoring**
- **Batch Processing**: Configurable chunk sizes
- **Rate Limiting**: Configurable delays between API calls
- **Memory Management**: Efficient data processing for large imports

## 🛠️ Configuration

### **Environment Variables**
```env
YOUCAN_SYNC_CHUNK_SIZE=100
YOUCAN_SYNC_DELAY=1
YOUCAN_DEFAULT_COMPANY_ID=1
```

### **Cache Configuration**
```php
// Geographic cache duration (1 hour)
const CACHE_DURATION = 3600;

// Clear cache when needed
$geoService = new GeographicMappingService();
$geoService->clearCache();
```

## 🎯 Benefits

1. **Zero Duplicates**: Intelligent detection prevents duplicate orders
2. **Complete Data**: Full geographic resolution for accurate addressing  
3. **Performance**: Caching and batch processing for efficiency
4. **Reliability**: Transaction safety and error handling
5. **Monitoring**: Comprehensive logging for troubleshooting
6. **Flexibility**: Configurable parameters and testing tools

## 🔄 Integration Points

- **Webhook Processing**: Real-time order import via YouCan webhooks
- **Manual Import**: On-demand import via admin interface
- **Batch Processing**: Background job processing with queue system
- **API Integration**: Uses YouCan CMS service for data retrieval
- **Geographic Services**: Intelligent mapping service for address resolution

This system ensures reliable, efficient, and intelligent order import from YouCan platform while maintaining data integrity and providing excellent performance.
