# CMS Integrations Table Structure

## New Tables

### cms_platforms Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Platform name (YouCan, Shopify, etc.) |
| slug | string | Unique identifier for the platform |
| is_active | boolean | Whether the platform is available for integration |
| required_fields | json | JSON array of required credential fields |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### company_cms_integrations Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| company_id | bigint | Foreign key to companies table |
| cms_platform_id | bigint | Foreign key to cms_platforms table |
| credentials | json | Encrypted JSON object with platform credentials |
| is_active | boolean | Whether this integration is active |
| settings | json | Additional integration settings |
| last_sync_at | timestamp | Last successful synchronization |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### company_cms_stores Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| company_id | bigint | Foreign key to companies table |
| company_cms_integration_id | bigint | Foreign key to company_cms_integrations table |
| store_id | string | External store ID from the CMS platform |
| store_name | string | Store name |
| store_url | string | Store URL |
| is_active | boolean | Whether this store is active |
| is_default | boolean | Whether this is the default store |
| metadata | json | Additional store metadata |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### cms_sync_logs Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| company_id | bigint | Foreign key to companies table |
| company_cms_integration_id | bigint | Foreign key to company_cms_integrations table |
| company_cms_store_id | bigint | Foreign key to company_cms_stores table |
| sync_type | string | Type of sync (orders, products, customers) |
| status | string | Status of sync (success, failed, in_progress) |
| items_processed | integer | Number of items processed |
| items_created | integer | Number of items created |
| items_updated | integer | Number of items updated |
| items_failed | integer | Number of items failed |
| started_at | timestamp | When sync started |
| completed_at | timestamp | When sync completed |
| error_message | text | Error message if failed |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |
