import { useState, useCallback } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import HeadingSmall from '@/components/heading-small';
import { type BreadcrumbItem } from '@/types';
import AppSidebarLayout from '@/layouts/app/app-sidebar-layout';
import SettingsLayout from '@/layouts/settings/layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Plus, Edit, Trash2, MoreHorizontal, DollarSign, Star } from 'lucide-react';

interface Currency {
    id: number;
    name: string;
    code: string;
    symbol: string;
    currency_position: 'left' | 'right';
    thousand_separator: string;
    decimal_separator: string;
    number_of_decimals: number;
    is_default: boolean;
    example: string;
}

interface Company {
    id: number;
    name: string;
    default_currency: string;
}

interface Props {
    currencies: Currency[];
    company: Company;
}

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Currency Settings',
        href: '/settings/currency-settings',
    },
];

export default function CurrencySettings({ currencies, company }: Props) {
    const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
    const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
    const [editingCurrency, setEditingCurrency] = useState<Currency | null>(null);

    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: '',
        code: '',
        symbol: '',
        currency_position: 'left' as 'left' | 'right',
        thousand_separator: ',',
        decimal_separator: '.',
        number_of_decimals: 2,
    });

    const handleAddCurrency = (e: React.FormEvent) => {
        e.preventDefault();
        post(route('settings.currency-settings.store'), {
            onSuccess: () => {
                setIsAddDialogOpen(false);
                reset();
            },
            onError: (errors) => {
                console.error('Add currency error:', errors);
            }
        });
    };

    const handleEditCurrency = (e: React.FormEvent) => {
        e.preventDefault();
        if (editingCurrency) {
            put(route('settings.currency-settings.update', editingCurrency.id), {
                onSuccess: () => {
                    setIsEditDialogOpen(false);
                    setEditingCurrency(null);
                    reset();
                },
                onError: (errors) => {
                    console.error('Edit currency error:', errors);
                }
            });
        }
    };

    const handleDelete = (currency: Currency) => {
        if (confirm('Are you sure you want to delete this currency?')) {
            router.delete(route('settings.currency-settings.destroy', currency.id), {
                onError: (errors) => {
                    console.error('Delete currency error:', errors);
                }
            });
        }
    };

    const handleSetDefault = (currency: Currency) => {
        router.patch(route('settings.currency-settings.set-default', currency.id), {
            onError: (errors) => {
                console.error('Set default currency error:', errors);
            }
        });
    };

    const openEditDialog = (currency: Currency) => {
        setEditingCurrency(currency);
        setData({
            name: currency.name,
            code: currency.code,
            symbol: currency.symbol,
            currency_position: currency.currency_position,
            thousand_separator: currency.thousand_separator,
            decimal_separator: currency.decimal_separator,
            number_of_decimals: currency.number_of_decimals,
        });
        setIsEditDialogOpen(true);
    };

    // Stable onChange handlers
    const handleNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setData('name', e.target.value);
    }, [setData]);

    const handleSymbolChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setData('symbol', e.target.value);
    }, [setData]);

    const handleCodeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setData('code', e.target.value.toUpperCase());
    }, [setData]);

    const handleThousandSeparatorChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setData('thousand_separator', e.target.value);
    }, [setData]);

    const handleDecimalSeparatorChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
        setData('decimal_separator', e.target.value);
    }, [setData]);





    return (
        <AppSidebarLayout breadcrumbs={breadcrumbs}>
            <Head title="Currency Settings" />

            <SettingsLayout>
                <div className="space-y-6">
                    <div className="flex items-center justify-between">
                        <HeadingSmall 
                            title="Currency Settings" 
                            description="Manage currencies for your company" 
                        />
                        
                        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
                            <DialogTrigger asChild>
                                <Button>
                                    <Plus className="mr-2 h-4 w-4" />
                                    Add New Currency
                                </Button>
                            </DialogTrigger>
                            <DialogContent className="max-w-2xl">
                                <DialogHeader>
                                    <DialogTitle>Add New Currency</DialogTitle>
                                    <DialogDescription>
                                        Add a new currency to your company settings.
                                    </DialogDescription>
                                </DialogHeader>
                                <form onSubmit={handleAddCurrency} className="space-y-4">
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <Label htmlFor="add-currency-name">Currency Name <span className="text-red-500">*</span></Label>
                                            <Input
                                                id="add-currency-name"
                                                name="name"
                                                type="text"
                                                value={data.name}
                                                onChange={handleNameChange}
                                                placeholder="e.g. Dollar"
                                                className="mt-1"
                                                autoComplete="off"
                                            />
                                            {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="add-currency-symbol">Currency Symbol <span className="text-red-500">*</span></Label>
                                            <Input
                                                id="add-currency-symbol"
                                                name="symbol"
                                                type="text"
                                                value={data.symbol}
                                                onChange={handleSymbolChange}
                                                placeholder="e.g. $"
                                                className="mt-1"
                                                autoComplete="off"
                                            />
                                            {errors.symbol && <p className="text-sm text-red-600 mt-1">{errors.symbol}</p>}
                                        </div>

                                        <div>
                                            <Label htmlFor="add-currency-code">Currency Code <span className="text-red-500">*</span></Label>
                                            <Input
                                                id="add-currency-code"
                                                name="code"
                                                type="text"
                                                value={data.code}
                                                onChange={handleCodeChange}
                                                placeholder="e.g. USD"
                                                maxLength={3}
                                                className="mt-1"
                                                autoComplete="off"
                                            />
                                            {errors.code && <p className="text-sm text-red-600 mt-1">{errors.code}</p>}
                                        </div>
                                    </div>

                                    <div className="space-y-4">
                                        <h4 className="text-lg font-medium">Currency Format Settings</h4>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="add-currency-position">Currency Position</Label>
                                                <Select value={data.currency_position} onValueChange={(value: 'left' | 'right') => setData('currency_position', value)}>
                                                    <SelectTrigger className="mt-1">
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="left">Left ($1,234.56)</SelectItem>
                                                        <SelectItem value="right">Right (1,234.56$)</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>

                                            <div>
                                                <Label htmlFor="add-thousand-separator">Thousand Separator</Label>
                                                <Input
                                                    id="add-thousand-separator"
                                                    name="thousand_separator"
                                                    type="text"
                                                    value={data.thousand_separator}
                                                    onChange={handleThousandSeparatorChange}
                                                    maxLength={1}
                                                    className="mt-1"
                                                    autoComplete="off"
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="add-decimal-separator">Decimal Separator</Label>
                                                <Input
                                                    id="add-decimal-separator"
                                                    name="decimal_separator"
                                                    type="text"
                                                    value={data.decimal_separator}
                                                    onChange={handleDecimalSeparatorChange}
                                                    maxLength={1}
                                                    className="mt-1"
                                                    autoComplete="off"
                                                />
                                            </div>

                                            <div>
                                                <Label htmlFor="add-number-of-decimals">Number of Decimals</Label>
                                                <Select value={data.number_of_decimals.toString()} onValueChange={(value) => setData('number_of_decimals', parseInt(value))}>
                                                    <SelectTrigger className="mt-1">
                                                        <SelectValue />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        <SelectItem value="0">0</SelectItem>
                                                        <SelectItem value="1">1</SelectItem>
                                                        <SelectItem value="2">2</SelectItem>
                                                        <SelectItem value="3">3</SelectItem>
                                                        <SelectItem value="4">4</SelectItem>
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Example Preview */}
                                    <div className="p-4 bg-gray-50 rounded-lg">
                                        <Label className="text-sm font-medium">Example</Label>
                                        <p className="text-lg font-mono mt-1">
                                            {data.currency_position === 'left'
                                                ? `${data.symbol || '$'}1${data.thousand_separator}234${data.thousand_separator}567${data.decimal_separator}${Array(data.number_of_decimals).fill('8').join('')}`
                                                : `1${data.thousand_separator}234${data.thousand_separator}567${data.decimal_separator}${Array(data.number_of_decimals).fill('8').join('')}${data.symbol || '$'}`
                                            }
                                        </p>
                                    </div>

                                    <DialogFooter>
                                        <Button
                                            type="button"
                                            variant="outline"
                                            onClick={() => {
                                                setIsAddDialogOpen(false);
                                                reset();
                                            }}
                                            disabled={processing}
                                        >
                                            Cancel
                                        </Button>
                                        <Button type="submit" disabled={processing}>
                                            {processing ? 'Saving...' : 'Save'}
                                        </Button>
                                    </DialogFooter>
                                </form>
                            </DialogContent>
                        </Dialog>
                    </div>

                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                Currencies
                            </CardTitle>
                            <CardDescription>
                                Manage your company's currencies. The default currency is used for new transactions.
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {currencies.length === 0 ? (
                                <div className="text-center py-8">
                                    <DollarSign className="mx-auto h-12 w-12 text-gray-400" />
                                    <h3 className="mt-2 text-sm font-medium text-gray-900">No currencies</h3>
                                    <p className="mt-1 text-sm text-gray-500">Get started by adding your first currency.</p>
                                </div>
                            ) : (
                                <Table>
                                    <TableHeader>
                                        <TableRow>
                                            <TableHead>Currency Name</TableHead>
                                            <TableHead>Symbol</TableHead>
                                            <TableHead>Code</TableHead>
                                            <TableHead>Currency Format</TableHead>
                                            <TableHead>Action</TableHead>
                                        </TableRow>
                                    </TableHeader>
                                    <TableBody>
                                        {currencies.map((currency) => (
                                            <TableRow key={currency.id}>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center gap-2">
                                                        {currency.name}
                                                        {currency.is_default && (
                                                            <Badge variant="default" className="text-xs">
                                                                <Star className="mr-1 h-3 w-3" />
                                                                Default
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </TableCell>
                                                <TableCell>{currency.symbol}</TableCell>
                                                <TableCell>{currency.code}</TableCell>
                                                <TableCell className="font-mono text-sm">{currency.example}</TableCell>
                                                <TableCell>
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                                <MoreHorizontal className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem onClick={() => {
                                                                try {
                                                                    openEditDialog(currency);
                                                                } catch (error) {
                                                                    console.error('Error opening edit dialog:', error);
                                                                }
                                                            }}>
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                Edit
                                                            </DropdownMenuItem>
                                                            {!currency.is_default && (
                                                                <>
                                                                    <DropdownMenuItem onClick={() => {
                                                                        try {
                                                                            handleSetDefault(currency);
                                                                        } catch (error) {
                                                                            console.error('Error setting default currency:', error);
                                                                        }
                                                                    }}>
                                                                        <Star className="mr-2 h-4 w-4" />
                                                                        Set as Default
                                                                    </DropdownMenuItem>
                                                                    <DropdownMenuItem
                                                                        onClick={() => {
                                                                            try {
                                                                                handleDelete(currency);
                                                                            } catch (error) {
                                                                                console.error('Error deleting currency:', error);
                                                                            }
                                                                        }}
                                                                        className="text-red-600"
                                                                    >
                                                                        <Trash2 className="mr-2 h-4 w-4" />
                                                                        Delete
                                                                    </DropdownMenuItem>
                                                                </>
                                                            )}
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))}
                                    </TableBody>
                                </Table>
                            )}
                        </CardContent>
                    </Card>

                    {/* Edit Dialog */}
                    <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
                        <DialogContent className="max-w-2xl">
                            <DialogHeader>
                                <DialogTitle>Edit Currency</DialogTitle>
                                <DialogDescription>
                                    Update the currency settings.
                                </DialogDescription>
                            </DialogHeader>
                            <form onSubmit={handleEditCurrency} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <Label htmlFor="edit-currency-name">Currency Name <span className="text-red-500">*</span></Label>
                                        <Input
                                            id="edit-currency-name"
                                            name="name"
                                            type="text"
                                            value={data.name}
                                            onChange={handleNameChange}
                                            placeholder="e.g. Dollar"
                                            className="mt-1"
                                            autoComplete="off"
                                        />
                                        {errors.name && <p className="text-sm text-red-600 mt-1">{errors.name}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="edit-currency-symbol">Currency Symbol <span className="text-red-500">*</span></Label>
                                        <Input
                                            id="edit-currency-symbol"
                                            name="symbol"
                                            type="text"
                                            value={data.symbol}
                                            onChange={handleSymbolChange}
                                            placeholder="e.g. $"
                                            className="mt-1"
                                            autoComplete="off"
                                        />
                                        {errors.symbol && <p className="text-sm text-red-600 mt-1">{errors.symbol}</p>}
                                    </div>

                                    <div>
                                        <Label htmlFor="edit-currency-code">Currency Code <span className="text-red-500">*</span></Label>
                                        <Input
                                            id="edit-currency-code"
                                            name="code"
                                            type="text"
                                            value={data.code}
                                            onChange={handleCodeChange}
                                            placeholder="e.g. USD"
                                            maxLength={3}
                                            className="mt-1"
                                            autoComplete="off"
                                        />
                                        {errors.code && <p className="text-sm text-red-600 mt-1">{errors.code}</p>}
                                    </div>
                                </div>

                                <div className="space-y-4">
                                    <h4 className="text-lg font-medium">Currency Format Settings</h4>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="edit-currency-position">Currency Position</Label>
                                            <Select value={data.currency_position} onValueChange={(value: 'left' | 'right') => setData('currency_position', value)}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="left">Left ($1,234.56)</SelectItem>
                                                    <SelectItem value="right">Right (1,234.56$)</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>

                                        <div>
                                            <Label htmlFor="edit-thousand-separator">Thousand Separator</Label>
                                            <Input
                                                id="edit-thousand-separator"
                                                name="thousand_separator"
                                                type="text"
                                                value={data.thousand_separator}
                                                onChange={handleThousandSeparatorChange}
                                                maxLength={1}
                                                className="mt-1"
                                                autoComplete="off"
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="edit-decimal-separator">Decimal Separator</Label>
                                            <Input
                                                id="edit-decimal-separator"
                                                name="decimal_separator"
                                                type="text"
                                                value={data.decimal_separator}
                                                onChange={handleDecimalSeparatorChange}
                                                maxLength={1}
                                                className="mt-1"
                                                autoComplete="off"
                                            />
                                        </div>

                                        <div>
                                            <Label htmlFor="edit-number-of-decimals">Number of Decimals</Label>
                                            <Select value={data.number_of_decimals.toString()} onValueChange={(value) => setData('number_of_decimals', parseInt(value))}>
                                                <SelectTrigger className="mt-1">
                                                    <SelectValue />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    <SelectItem value="0">0</SelectItem>
                                                    <SelectItem value="1">1</SelectItem>
                                                    <SelectItem value="2">2</SelectItem>
                                                    <SelectItem value="3">3</SelectItem>
                                                    <SelectItem value="4">4</SelectItem>
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    </div>

                                    {/* Example Preview */}
                                    <div className="p-4 bg-gray-50 rounded-lg">
                                        <Label className="text-sm font-medium">Example</Label>
                                        <p className="text-lg font-mono mt-1">
                                            {data.currency_position === 'left'
                                                ? `${data.symbol || '$'}1${data.thousand_separator}234${data.thousand_separator}567${data.decimal_separator}${Array(data.number_of_decimals).fill('8').join('')}`
                                                : `1${data.thousand_separator}234${data.thousand_separator}567${data.decimal_separator}${Array(data.number_of_decimals).fill('8').join('')}${data.symbol || '$'}`
                                            }
                                        </p>
                                    </div>
                                </div>

                                <DialogFooter>
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => {
                                            setIsEditDialogOpen(false);
                                            setEditingCurrency(null);
                                            reset();
                                        }}
                                        disabled={processing}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={processing}>
                                        {processing ? 'Saving...' : 'Save'}
                                    </Button>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>
            </SettingsLayout>
        </AppSidebarLayout>
    );
}
