@echo off
REM Backup Geographic Data Script for Windows
REM Usage: scripts\backup-geo-data.bat

echo 🌍 Backing up geographic data...

REM Read database connection details from .env
for /f "tokens=2 delims==" %%a in ('findstr "DB_HOST" .env') do set DB_HOST=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PORT" .env') do set DB_PORT=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_DATABASE" .env') do set DB_DATABASE=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_USERNAME" .env') do set DB_USERNAME=%%a
for /f "tokens=2 delims==" %%a in ('findstr "DB_PASSWORD" .env') do set DB_PASSWORD=%%a

REM Create backup directory if it doesn't exist
if not exist "storage\backups" mkdir "storage\backups"

REM Set PGPASSWORD environment variable
set PGPASSWORD=%DB_PASSWORD%

REM Backup countries, states, and cities tables
echo 📦 Backing up countries table...
pg_dump -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -t countries --data-only --inserts > storage\backups\countries.sql

echo 📦 Backing up states table...
pg_dump -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -t states --data-only --inserts > storage\backups\states.sql

echo 📦 Backing up cities table...
pg_dump -h %DB_HOST% -p %DB_PORT% -U %DB_USERNAME% -d %DB_DATABASE% -t cities --data-only --inserts > storage\backups\cities.sql

echo ✅ Geographic data backup completed!
echo 📁 Backup files saved in storage\backups\

REM Clear password from environment
set PGPASSWORD=

pause
