<?php

namespace App\Services\YoucanAPI;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class YoucanService
{
    protected $baseUrl;
    protected $email;
    protected $password;
    protected $clientId;
    protected $accessToken;

    public function __construct()
    {
        $this->baseUrl = config('youcan.api_url');
        $this->email = config('youcan.email');
        $this->password = config('youcan.password');
        $this->clientId = config('youcan.client_id');

        Log::info('YouCan Service Initialized', [
            'base_url' => $this->baseUrl,
            'client_id' => $this->clientId,
            'email' => $this->email,
            'environment' => app()->environment(),
            'ssl_verify' => !app()->environment('local'),
            'curl_version' => curl_version(),
            'php_version' => PHP_VERSION
        ]);

        $this->accessToken = $this->getAccessToken();
    }

    /**
     * Authenticate with YouCan API and get access token
     *
     * @return string|null
     */
    protected function authenticate()
    {
        try {
            Log::info('Starting YouCan authentication', [
                'email' => $this->email,
                'base_url' => $this->baseUrl
            ]);

            $response = Http::withOptions([
                'verify' => !app()->environment('local')
            ])->post($this->baseUrl . '/auth/login', [
                'email' => $this->email,
                'password' => $this->password
            ]);

            Log::info('YouCan authentication response', [
                'status' => $response->status(),
                'body' => $response->json(),
                'headers' => $response->headers()
            ]);

            if ($response->successful()) {
                $data = $response->json();
                $token = $data['token'] ?? null;

                if ($token) {
                    Log::info('YouCan authentication successful, token received');
                    Cache::put('youcan_access_token_' . md5($this->email), $token, now()->addHour());
                    return $token;
                }
            }

            Log::error('YouCan Authentication Failed', [
                'status' => $response->status(),
                'body' => $response->json()
            ]);

            throw new \Exception('Failed to authenticate with YouCan API: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('YouCan Authentication Exception: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get access token from cache or authenticate to get new one
     *
     * @return string|null
     */
    protected function getAccessToken()
    {
        return Cache::remember('youcan_access_token', 60 * 24, function () {
            return $this->authenticate();
        });
    }

    /**
     * Make an authenticated request to YouCan API
     *
     * @param string $method
     * @param string $endpoint
     * @param array $data
     * @return array|null
     */
    protected function makeRequest($method, $endpoint, $data = [])
    {
        try {
            if (!$this->accessToken) {
                Log::info('No access token found, attempting authentication');
                $this->accessToken = $this->authenticate();
            }

            $url = $this->baseUrl . $endpoint;
            Log::info('Making YouCan API request', [
                'method' => $method,
                'url' => $url,
                'data' => $data
            ]);

            $response = Http::withOptions([
                'verify' => !app()->environment('local')
            ])->withHeaders([
                'Authorization' => 'Bearer ' . $this->accessToken,
                'Accept' => 'application/json'
            ])->$method($url, $data);

            Log::info('YouCan API response received', [
                'status' => $response->status(),
                'headers' => $response->headers(),
                'body' => $response->json()
            ]);

            // Consider any 2xx status code as successful
            if ($response->successful()) {
                return $response->json() ?: ['success' => true];
            }

            if ($response->status() === 401) {
                Log::info('Token expired, refreshing...');
                Cache::forget('youcan_access_token_' . md5($this->email));
                $this->accessToken = $this->authenticate();
                
                // Retry the request
                return $this->makeRequest($method, $endpoint, $data);
            }

            Log::error('YouCan API Request Failed', [
                'method' => $method,
                'endpoint' => $endpoint,
                'status' => $response->status(),
                'body' => $response->json()
            ]);

            throw new \Exception('Failed to make YouCan API request: ' . $response->body());

        } catch (\Exception $e) {
            Log::error('YouCan API Request Exception: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Get orders from YouCan platform
     *
     * @param array $params
     * @return array
     */
    public function getOrders(array $params = [])
    {
        // Add required includes for order data
        $params['include'] = 'variants.variant.product,customer';
        
        // Add date filters if provided
        if (!empty($params['created_at_min'])) {
            $params['filters[0][field]'] = 'created_at_from';
            $params['filters[0][value]'] = $params['created_at_min'];
            unset($params['created_at_min']);
        }
        
        if (!empty($params['created_at_max'])) {
            $params['filters[1][field]'] = 'created_at_to';
            $params['filters[1][value]'] = $params['created_at_max'];
            unset($params['created_at_max']);
        }

        // Set default limit if not provided
        if (empty($params['limit'])) {
            $params['limit'] = 100;
        }

        return $this->makeRequest('get', '/orders', $params);
    }

    /**
     * Get a single order from YouCan platform
     *
     * @param string $orderId
     * @return array|null
     */
    public function getOrder(string $orderId)
    {
        return $this->makeRequest('get', "/orders/{$orderId}");
    }

    /**
     * Get order statuses from YouCan platform
     *
     * @return array
     */
    public function getOrderStatuses()
    {
        return $this->makeRequest('get', '/orders/settings');
    }

    /**
     * Subscribe to YouCan webhooks
     *
     * @param array $data
     * @return array
     */
    public function subscribeToWebhook(array $data)
    {
        return $this->makeRequest('post', '/resthooks/subscribe', $data);
    }

    /**
     * List all webhook subscriptions
     *
     * @return array
     */
    public function listWebhooks()
    {
        return $this->makeRequest('get', '/resthooks/list');
    }

    /**
     * Delete a webhook subscription
     *
     * @param string $webhookId
     * @return array
     */
    public function deleteWebhook(string $webhookId)
    {
        return $this->makeRequest('delete', "/resthooks/unsubscribe/{$webhookId}");
    }

    public function getProducts()
    {
        return $this->makeRequest('get','/products?page=1&include=variants.variant.product,customer&limit=10') ;
    }

    public function getCustomers(array $params = [])
    {
        return $this->makeRequest('get', '/customers', $params);
    }
} 

