# Orders Module

## Overview
The Orders module is the core of the order management system, allowing users to create, track, and manage customer orders throughout their lifecycle.

## Features
- Order creation and management
- Order status tracking and updates
- Order history and audit trail
- Order filtering and search
- Order details view

## Database Structure

### Orders Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| order_number | string | Unique order identifier |
| customer_id | bigint | Foreign key to customers table |
| order_status_id | bigint | Foreign key to order_statuses table |
| total_amount | decimal | Total order amount |
| shipping_address | text | Shipping address |
| billing_address | text | Billing address |
| payment_method | string | Payment method used |
| payment_status | string | Status of payment |
| notes | text | Order notes |
| company_id | bigint | Foreign key to companies table |
| created_by | bigint | User who created the order |
| updated_by | bigint | User who last updated the order |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Order_Items Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| order_id | bigint | Foreign key to orders table |
| product_id | bigint | Foreign key to products table |
| quantity | integer | Quantity ordered |
| unit_price | decimal | Price per unit |
| total_price | decimal | Total price for this item |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Order_Statuses Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Status name |
| display_name | string | Display name for UI |
| color | string | Color code for UI display |
| order | integer | Display order |
| can_affect_real_stock | boolean | Whether status affects real stock |
| affect_real_stockby | string | How it affects real stock |
| can_affect_virtual_stock | boolean | Whether status affects virtual stock |
| affect_virtual_stockby | string | How it affects virtual stock |
| related_statuses | string | Related status IDs |
| is_active | boolean | Whether status is active |
| is_default | boolean | Whether status is default |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

## User Interface
- Orders listing page with search, filtering, and sorting
- Order creation form
- Order details view
- Order status management
- Order history timeline

## Business Rules
- Orders are always associated with a customer
- Orders must have at least one order item
- Order status changes may trigger inventory adjustments
- Order numbers must be unique within a company
- Order history must be maintained for audit purposes