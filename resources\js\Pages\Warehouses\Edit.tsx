import { Head, useForm, router } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { ArrowLeft } from 'lucide-react';

interface Warehouse {
    id: number;
    name: string;
    phone: string | null;
    email: string | null;
    address: string | null;
    is_active: boolean;
    is_default: boolean;
   
}

interface Props {
    warehouse: Warehouse;
    errors?: Record<string, string>;
}

export default function Edit({ warehouse, errors }: Props) {
    const form = useForm({
        name: warehouse.name,
        phone: warehouse.phone || '',
        email: warehouse.email || '',
        address: warehouse.address || '',
        is_active: warehouse.is_active,
        is_default: warehouse.is_default,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.put(route('warehouses.update', warehouse.id));
    };

    return (
        <AppSidebarLayout>
            <Head title={`Edit Warehouse - ${warehouse.name}`} />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div className="flex items-center gap-4">
                        <Button
                            variant="outline"
                            size="icon"
                            onClick={() => window.history.back()}
                        >
                            <ArrowLeft className="h-4 w-4" />
                        </Button>
                        <div>
                            <h1 className="text-2xl font-semibold">Edit Warehouse</h1>
                            <p className="text-sm text-muted-foreground">Update warehouse information</p>
                        </div>
                    </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Warehouse Information</CardTitle>
                            <CardDescription>
                                Update the warehouse's basic information
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Warehouse Name</Label>
                                    <Input
                                        id="name"
                                        placeholder="Enter warehouse name"
                                        value={form.data.name}
                                        onChange={e => form.setData('name', e.target.value)}
                                    />
                                    {errors?.name && (
                                        <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        placeholder="Enter phone number"
                                        value={form.data.phone}
                                        onChange={e => form.setData('phone', e.target.value)}
                                    />
                                    {errors?.phone && (
                                        <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                                    )}
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Enter email address"
                                    value={form.data.email}
                                    onChange={e => form.setData('email', e.target.value)}
                                />
                                {errors?.email && (
                                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                                )}
                            </div>
                            
                            <div>
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    placeholder="Enter warehouse address"
                                    value={form.data.address}
                                    onChange={e => form.setData('address', e.target.value)}
                                />
                                {errors?.address && (
                                    <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                                )}
                            </div>
                            
                          
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_active"
                                        checked={form.data.is_active}
                                        onCheckedChange={(checked) => {
                                            if (!checked && form.data.is_default) {
                                                return;
                                            }
                                            form.setData('is_active', checked);
                                        }}
                                    />
                                    <Label htmlFor="is_active">Active</Label>
                                </div>
                                
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_default"
                                        checked={form.data.is_default}
                                        onCheckedChange={(checked) => {
                                            if (checked) {
                                                form.setData('is_active', true);
                                            }
                                            form.setData('is_default', checked);
                                        }}
                                    />
                                    <Label htmlFor="is_default">Default</Label>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" onClick={() => window.history.back()}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={form.processing}>
                            {form.processing ? 'Saving...' : 'Save Changes'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppSidebarLayout>
    );
} 