# YouCan Webhook & Order Import Testing Guide (200 Orders)

## 🎯 **Quick Testing Setup**

This guide will help you test YouCan webhook functionality and import exactly 200 orders for testing purposes.

## 📋 **Prerequisites Checklist**

- [ ] Laravel application running locally
- [ ] YouCan store with API access credentials
- [ ] ngrok installed (for webhook testing)
- [ ] Database with companies and CMS integrations configured

## 🚀 **Step 1: Environment Setup**

### 1.1 Install ngrok (if not installed)
```bash
# Windows (Chocolatey)
choco install ngrok

# macOS (Homebrew)  
brew install ngrok

# Linux (Snap)
snap install ngrok

# Or download from: https://ngrok.com/download
```

### 1.2 Configure ngrok
```bash
# Sign up at https://ngrok.com and get your auth token
ngrok config add-authtoken YOUR_AUTH_TOKEN_HERE

# Verify installation
ngrok version
```

### 1.3 Update .env file
```env
# Add these to your .env file
YOUCAN_API_URL=https://api.youcan.shop
YOUCAN_EMAIL=<EMAIL>
YOUCAN_PASSWORD=your_youcan_password
YOUCAN_CLIENT_ID=your_client_id
YOUCAN_CLIENT_SECRET=your_client_secret

# For webhook testing (will be updated with ngrok URL)
YOUCAN_NGROK_URL=
APP_URL=http://localhost:8000

# Queue configuration for background processing
QUEUE_CONNECTION=database

# Default company for testing
YOUCAN_DEFAULT_COMPANY_ID=1
```

## 🚀 **Step 2: Start Development Environment**

### 2.1 Start Laravel (Terminal 1)
```bash
# Start Laravel development server
php artisan serve --host=0.0.0.0 --port=8000

# Keep this terminal running
```

### 2.2 Start Queue Worker (Terminal 2)
```bash
# Start queue worker for background jobs
php artisan queue:work --tries=3 --timeout=300

# Keep this terminal running
```

### 2.3 Start ngrok Tunnel (Terminal 3)
```bash
# Start ngrok tunnel
ngrok http 8000

# Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
# Update your .env file with this URL:
# YOUCAN_NGROK_URL=https://abc123.ngrok.io
# APP_URL=https://abc123.ngrok.io
```

### 2.4 Monitor Logs (Terminal 4)
```bash
# Monitor Laravel logs in real-time
tail -f storage/logs/laravel.log
```

## 🚀 **Step 3: Configure YouCan Integration**

### 3.1 Setup CMS Integration (via Web Interface)
1. Login to your application
2. Go to Settings → CMS Integrations
3. Configure YouCan integration with your credentials
4. Test the connection

### 3.2 Alternative: Manual Database Setup
```bash
php artisan tinker
```

```php
// Create CMS platform if not exists
$platform = App\Models\CmsPlatform::firstOrCreate([
    'slug' => 'youcan'
], [
    'name' => 'YouCan',
    'api_base_url' => 'https://api.youcan.shop',
    'is_active' => true,
    'required_fields' => ['email', 'password', 'client_id', 'client_secret']
]);

// Create integration for your company
$integration = App\Models\CompanyCmsIntegration::create([
    'company_id' => 1, // Your company ID
    'cms_platform_id' => $platform->id,
    'credentials' => [
        'email' => '<EMAIL>',
        'password' => 'your_youcan_password',
        'client_id' => 'your_client_id',
        'client_secret' => 'your_client_secret'
    ],
    'is_active' => true
]);
```

## 🚀 **Step 4: Test Order Import (200 Orders)**

### 4.1 Manual Import Test
```bash
# Test import with 200 orders limit
php artisan youcan:test-import --company-id=1

# When prompted for number of orders, enter: 200
```

### 4.2 Alternative: Direct Job Dispatch
```bash
php artisan tinker
```

```php
// Import 200 orders from YouCan
App\Jobs\SyncYoucanOrders::dispatch(1, [
    'limit' => 200,
    'page' => 1
]);

// Check job status
echo "Job dispatched! Check queue worker terminal for progress.";
```

### 4.3 Import with Date Range
```bash
php artisan tinker
```

```php
// Import orders from last 30 days (max 200)
App\Jobs\SyncYoucanOrders::dispatch(1, [
    'limit' => 200,
    'created_at_min' => now()->subDays(30)->format('Y-m-d'),
    'created_at_max' => now()->format('Y-m-d')
]);
```

## 🚀 **Step 5: Setup Webhook Testing**

### 5.1 Subscribe to Webhooks
```bash
# Via web interface
# Go to YouCan Import page and click "Subscribe to Webhooks"

# Or via API call
curl -X POST http://localhost:8000/youcan/webhook-subscribe \
     -H "Authorization: Bearer YOUR_API_TOKEN" \
     -H "Content-Type: application/json"
```

### 5.2 Test Webhook Endpoint
```bash
# Test webhook endpoint manually
curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \
     -H "Content-Type: application/json" \
     -d '{
       "event": "order.create",
       "data": {
         "id": "test-order-123",
         "ref": "TEST-001"
       }
     }'
```

### 5.3 Check Webhook Status
```bash
# Check current webhook subscriptions
curl -X GET http://localhost:8000/youcan/webhook-status \
     -H "Authorization: Bearer YOUR_API_TOKEN"
```

## 🚀 **Step 6: Monitor Testing Progress**

### 6.1 Database Monitoring
```bash
php artisan tinker
```

```php
// Check imported orders count
$count = App\Models\Order::where('source', 'youcan')->count();
echo "Imported YouCan orders: {$count}";

// Check recent orders
$recent = App\Models\Order::where('source', 'youcan')
    ->latest()
    ->take(10)
    ->get(['id', 'ref', 'youcan_order_id', 'total_price', 'created_at']);
    
$recent->each(function($order) {
    echo "Order #{$order->id}: {$order->ref} - {$order->total_price} - {$order->created_at}\n";
});

// Check customers created
$customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count();
echo "YouCan customers: {$customerCount}";
```

### 6.2 Log Monitoring Commands
```bash
# Monitor order processing
tail -f storage/logs/laravel.log | grep -i "order"

# Monitor YouCan API calls
tail -f storage/logs/laravel.log | grep -i "youcan"

# Monitor webhook activity
tail -f storage/logs/laravel.log | grep -i "webhook"

# Monitor job processing
tail -f storage/logs/laravel.log | grep -i "SyncYoucanOrders"
```

## 🚀 **Step 7: Test Webhook with Real Orders**

### 7.1 Create Test Order in YouCan
1. Login to your YouCan admin panel
2. Create a new test order
3. Monitor your webhook logs for real-time processing

### 7.2 Verify Webhook Processing
```bash
# Check if webhook was received
grep "YouCan webhook received" storage/logs/laravel.log | tail -5

# Check if order was processed
grep "Order created successfully" storage/logs/laravel.log | tail -5
```

## 🔧 **Troubleshooting**

### Common Issues:

**1. Orders not importing:**
```bash
# Check YouCan API connection
php artisan youcan:test-import --company-id=1 --test-connection

# Check integration credentials
php artisan tinker
>>> App\Models\CompanyCmsIntegration::where('company_id', 1)->first()
```

**2. Webhook not receiving:**
```bash
# Check ngrok dashboard: http://127.0.0.1:4040
# Verify webhook subscription
# Check Laravel route list
php artisan route:list | grep webhook
```

**3. Queue jobs failing:**
```bash
# Check failed jobs
php artisan queue:failed

# Retry failed jobs
php artisan queue:retry all
```

## ✅ **Success Indicators**

- [ ] 200 orders imported from YouCan
- [ ] Customers automatically created/matched
- [ ] Geographic data properly resolved
- [ ] No duplicate orders created
- [ ] Webhook endpoint receiving test calls
- [ ] Real-time order creation via webhook working
- [ ] All logs showing successful processing

## 📊 **Final Verification**

```bash
php artisan tinker
```

```php
// Final statistics
echo "=== YouCan Import Test Results ===\n";
echo "Orders imported: " . App\Models\Order::where('source', 'youcan')->count() . "\n";
echo "Customers created: " . App\Models\Customer::whereNotNull('youcan_customer_id')->count() . "\n";
echo "Latest order: " . App\Models\Order::where('source', 'youcan')->latest()->first()?->ref . "\n";
echo "Webhook subscribed: " . (App\Models\YoucanWebhookSubscription::exists() ? 'Yes' : 'No') . "\n";
```

This setup will allow you to thoroughly test both the manual order import (200 orders) and real-time webhook functionality! 🚀
