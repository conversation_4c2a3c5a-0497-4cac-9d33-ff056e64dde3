import { useState } from 'react';
import { Head, useForm, router } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';

interface Props {
    errors?: Record<string, string>;
}

type FormData = {
    name: string;
    phone: string;
    email: string;
    address: string;
    is_active: boolean;
    is_default: boolean;
}

export default function Create({ errors }: Props) {
    const form = useForm<FormData>({
        name: '',
        phone: '',
        email: '',
        address: '',
        is_active: true,
        is_default: false,
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        form.post(route('warehouses.store'));
    };

    return (
        <AppSidebarLayout>
            <Head title="Create Warehouse" />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                        <h1 className="text-2xl font-semibold">Create Warehouse</h1>
                        <p className="text-sm text-muted-foreground">Add a new warehouse to your company</p>
                    </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                    <Card>
                        <CardHeader>
                            <CardTitle>Warehouse Information</CardTitle>
                            <CardDescription>
                                Enter the warehouse's basic information
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <Label htmlFor="name">Warehouse Name</Label>
                                    <Input
                                        id="name"
                                        placeholder="Enter warehouse name"
                                        value={form.data.name}
                                        onChange={e => form.setData('name', e.target.value)}
                                    />
                                    {errors?.name && (
                                        <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                                    )}
                                </div>
                                
                                <div>
                                    <Label htmlFor="phone">Phone Number</Label>
                                    <Input
                                        id="phone"
                                        placeholder="Enter phone number"
                                        value={form.data.phone}
                                        onChange={e => form.setData('phone', e.target.value)}
                                    />
                                    {errors?.phone && (
                                        <p className="text-red-500 text-sm mt-1">{errors.phone}</p>
                                    )}
                                </div>
                            </div>
                            
                            <div>
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Enter email address"
                                    value={form.data.email}
                                    onChange={e => form.setData('email', e.target.value)}
                                />
                                {errors?.email && (
                                    <p className="text-red-500 text-sm mt-1">{errors.email}</p>
                                )}
                            </div>
                            
                            <div>
                                <Label htmlFor="address">Address</Label>
                                <Textarea
                                    id="address"
                                    placeholder="Enter warehouse address"
                                    value={form.data.address}
                                    onChange={e => form.setData('address', e.target.value)}
                                />
                                {errors?.address && (
                                    <p className="text-red-500 text-sm mt-1">{errors.address}</p>
                                )}
                            </div>
                            
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_active"
                                        checked={form.data.is_active}
                                        onCheckedChange={(checked) => form.setData('is_active', checked)}
                                    />
                                    <Label htmlFor="is_active">Active</Label>
                                </div>
                                
                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_default"
                                        checked={form.data.is_default}
                                        onCheckedChange={(checked) => form.setData('is_default', checked)}
                                    />
                                    <Label htmlFor="is_default">Default</Label>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                    
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" onClick={() => window.history.back()}>
                            Cancel
                        </Button>
                        <Button type="submit" disabled={form.processing}>
                            {form.processing ? 'Creating...' : 'Create Warehouse'}
                        </Button>
                    </div>
                </form>
            </div>
        </AppSidebarLayout>
    );
} 