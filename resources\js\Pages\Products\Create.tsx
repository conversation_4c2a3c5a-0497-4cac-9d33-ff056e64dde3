import { useState } from 'react';
import { Head, useForm, router, FormOptions } from '@inertiajs/react';
import AppSidebarLayout from "@/layouts/app/app-sidebar-layout";
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select';
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
} from "@/components/ui/command";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import {
    Dialog,
    DialogContent,
    DialogDescription,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogFooter,
} from "@/components/ui/dialog";
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card';
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON><PERSON>,
    TabsTrigger,
} from '@/components/ui/tabs';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Plus, Trash2, Upload, Check, ChevronsUpDown } from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { MultiSelect } from '@/components/ui/multi-select';

interface VariantFormValues {
    sku: string;
    variant_combination: Record<string, string>;
    additional_purchase_price: number;
    additional_sell_price: number;
    additional_negotiation_price: number;
    total_qty: number;
    status: 'active' | 'inactive';
    image?: File;
}

interface Product {
    id: number;
    name: string;
    code: string;
    purchase_price: number;
    sell_price: number;
    product_type: 'standard' | 'combo';
}

interface Props {
    products: Product[];
}

interface MultiSelectOption {
    value: string | number;
    label: string;
    icon?: React.ComponentType<{ className?: string }>;
}

type FormData = {
    [key: string]: any;
    name: string;
    code: string;
    product_type: 'standard' | 'combo';
    purchase_price: number;
    sell_price: number;
    negotiation_price: number;
    delivery_price: number;
    alert_quantity: number;
    is_variant: boolean;
    variant_options: string[];
    product_details: string;
    product_notes: string;
    status: 'active' | 'inactive';
    image: File | null;
    variants: VariantFormValues[];
    product_list: number[];
}

// Add this helper component for consistent error message styling
const FormError = ({ message }: { message?: string }) => {
    if (!message) return null;
    return <p className="text-sm text-destructive mt-1">{message}</p>;
};

export default function Create({ products = [] }: Props) {
    console.log('Create component render - Available products:', products);

    const [activeTab, setActiveTab] = useState('basic');
    const [variants, setVariants] = useState<VariantFormValues[]>([]);
    const [variantOptions, setVariantOptions] = useState<Record<string, string[]>>({});
    const [previewImage, setPreviewImage] = useState<string | null>(null);
    const [variantPreviewImages, setVariantPreviewImages] = useState<Record<number, string | null>>({});
    const [comboOpen, setComboOpen] = useState(false);
    const [isOptionDialogOpen, setIsOptionDialogOpen] = useState(false);
    const [newOptionName, setNewOptionName] = useState('');
    const [newOptionValues, setNewOptionValues] = useState('');
    const [editingOption, setEditingOption] = useState<string | null>(null);

    const form = useForm<FormData>({
            name: '',
            code: '',
            product_type: 'standard',
            purchase_price: 0,
            sell_price: 0,
            negotiation_price: 0,
            delivery_price: 0,
            alert_quantity: 0,
            is_variant: false,
            variant_options: [],
            product_details: '',
            product_notes: '',
            status: 'active',
        image: null,
        variants: [],
        product_list: [],
    });

    const { data, setData, post, processing, errors } = form;

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        const formData = new FormData();
        
        // Basic fields
        formData.append('name', data.name);
        formData.append('code', data.code);
        formData.append('product_type', data.product_type);
        formData.append('purchase_price', String(data.purchase_price));
        formData.append('sell_price', String(data.sell_price));
        formData.append('negotiation_price', String(data.negotiation_price));
        formData.append('delivery_price', String(data.delivery_price));
        formData.append('alert_quantity', String(data.alert_quantity));
        formData.append('status', data.status);
        
        // Boolean fields
        formData.append('is_variant', data.is_variant ? '1' : '0');
        
        // Array fields
        if (data.is_variant && Array.isArray(data.variant_options)) {
            formData.append('variant_options', JSON.stringify(data.variant_options));
        }
        
        // Optional fields
        if (data.product_details) {
            formData.append('product_details', data.product_details);
        }
        if (data.product_notes) {
            formData.append('product_notes', data.product_notes);
        }
        
        // Handle image if present
        if (data.image instanceof File) {
            formData.append('image', data.image);
        }
        
        // Handle variants if is_variant is true
        if (data.is_variant && Array.isArray(data.variants)) {
            data.variants.forEach((variant, index) => {
                Object.entries(variant).forEach(([key, value]) => {
                    if (key === 'variant_combination') {
                        formData.append(`variants[${index}][${key}]`, JSON.stringify(value));
                    } else if (key === 'image' && value instanceof File) {
                        formData.append(`variants[${index}][${key}]`, value);
                    } else {
                        formData.append(`variants[${index}][${key}]`, String(value));
                    }
                });
            });
        }
        
        // Handle product_list for combo products
        if (data.product_type === 'combo' && Array.isArray(data.product_list)) {
            formData.append('product_list', JSON.stringify(data.product_list));
        }

        post(route('products.store'), formData as unknown as FormOptions);
    };

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('image', file);
            // Create a temporary URL for preview
            const reader = new FileReader();
            reader.onloadend = () => {
                setPreviewImage(reader.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleProductTypeChange = (value: string) => {
        if (value === 'combo') {
            setData({
                ...data,
                product_type: 'combo',
                is_variant: false,
                variants: [],
                variant_options: [],
            });
        } else if (value === 'standard') {
            setData({
                ...data,
                product_type: 'standard',
                product_list: [],
            });
        }
    };

    const handleStatusChange = (value: string) => {
        if (value === 'active' || value === 'inactive') {
            setData('status', value);
        }
    };

    const calculatePrices = (selectedIds: number[]) => {
        try {
            const totalPurchasePrice = selectedIds.reduce((sum, id) => {
                const product = products.find(p => p.id === id);
                const price = product ? parseFloat(String(product.purchase_price)) : 0;
                return sum + (isNaN(price) ? 0 : price);
            }, 0);

            const totalSellPrice = selectedIds.reduce((sum, id) => {
                const product = products.find(p => p.id === id);
                const price = product ? parseFloat(String(product.sell_price)) : 0;
                return sum + (isNaN(price) ? 0 : price);
            }, 0);

            return {
                purchase_price: totalPurchasePrice,
                sell_price: totalSellPrice,
                negotiation_price: totalSellPrice * 0.9,
                delivery_price: totalSellPrice * 0.05
            };
        } catch (error) {
            console.error('Error calculating prices:', error);
            return {
                purchase_price: 0,
                sell_price: 0,
                negotiation_price: 0,
                delivery_price: 0
            };
        }
    };

    const handleProductSelection = (values: (string | number)[]) => {
        try {
            const selectedIds = values.map(v => Number(v));
            const prices = calculatePrices(selectedIds);
            
            setData({
                ...data,
                product_list: selectedIds,
                ...prices
            });
        } catch (error) {
            console.error('Error in product selection:', error);
        }
    };

    // Add helper function to generate SKU from combination
    const generateSkuFromCombination = (combination: Record<string, string>): string => {
        return Object.values(combination).join('-');
    };

    // Update the generateAllCombinations function
    const generateAllCombinations = () => {
        const options = Object.entries(variantOptions);
        if (options.length === 0) return;

        const generateCombos = (arr: [string, string[]][]): Record<string, string>[] => {
            if (arr.length === 0) return [{}];
            const [current, ...rest] = arr;
            const [optionName, values] = current;
            const restCombos = generateCombos(rest);
            return values.flatMap(value => 
                restCombos.map(combo => ({
                    ...combo,
                    [optionName]: value
                }))
            );
        };

        const combinations = generateCombos(options);
        
        // Filter out combinations that already exist
        const existingCombinations = variants.map(v => JSON.stringify(v.variant_combination));
        const newCombinations = combinations.filter(
            combo => !existingCombinations.includes(JSON.stringify(combo))
        );

        // Create variants for new combinations
        const newVariants: VariantFormValues[] = newCombinations.map(combination => ({
            sku: generateSkuFromCombination(combination),
            variant_combination: combination,
            additional_purchase_price: 0,
            additional_sell_price: 0,
            additional_negotiation_price: 0,
            total_qty: 0,
            status: 'active'
        }));

        setVariants([...variants, ...newVariants]);
        setData('variants', [...variants, ...newVariants]);
    };

    // Update the addVariant function
    const addVariant = () => {
        const newVariant: VariantFormValues = {
            sku: '',
            variant_combination: Object.fromEntries(
                Object.keys(variantOptions).map(key => [key, ''])
            ),
            additional_purchase_price: 0,
            additional_sell_price: 0,
            additional_negotiation_price: 0,
            total_qty: 0,
            status: 'active'
        };
        const updatedVariants = [...variants, newVariant];
        setVariants(updatedVariants);
        setData('variants', updatedVariants);
    };

    // Helper function to check if all possible combinations are used
    const areAllCombinationsTaken = () => {
        const options = Object.entries(variantOptions);
        if (options.length === 0) return false;

        const totalPossibleCombinations = options.reduce(
            (acc, [_, values]) => acc * values.length,
            1
        );

        return variants.length >= totalPossibleCombinations;
    };

    // Remove a variant
    const removeVariant = (index: number) => {
        const updatedVariants = variants.filter((_, i) => i !== index);
        setVariants(updatedVariants);
        setData('variants', updatedVariants);
        
        // Update preview images
        const updatedPreviewImages = { ...variantPreviewImages };
        delete updatedPreviewImages[index];
        setVariantPreviewImages(updatedPreviewImages);
    };

    // Update a variant
    const updateVariant = (index: number, field: keyof VariantFormValues, value: any) => {
        const updatedVariants = [...variants];
        updatedVariants[index] = {
            ...updatedVariants[index],
            [field]: value,
        };
        setVariants(updatedVariants);
        setData('variants', updatedVariants);
    };

    // Handle variant image change
    const handleVariantImageChange = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            updateVariant(index, 'image', file);
            
            // Create a temporary URL for preview
            const reader = new FileReader();
            reader.onloadend = () => {
                setVariantPreviewImages({
                    ...variantPreviewImages,
                    [index]: reader.result as string,
                });
            };
            reader.readAsDataURL(file);
        }
    };

    // Add a new variant option
    const addVariantOption = () => {
        if (editingOption) {
            const values = newOptionValues.split(',').map(v => v.trim());
                setVariantOptions({
                    ...variantOptions,
                [editingOption]: values
            });
        } else if (newOptionName && newOptionValues) {
            const values = newOptionValues.split(',').map(v => v.trim());
            setVariantOptions({
                ...variantOptions,
                [newOptionName]: values
            });
            // Update variant_options array
            setData('variant_options', [...(Array.isArray(data.variant_options) ? data.variant_options : []), newOptionName]);
        }
        setIsOptionDialogOpen(false);
        setNewOptionName('');
        setNewOptionValues('');
        setEditingOption(null);
    };

    // Remove a variant option
    const removeVariantOption = (optionName: string) => {
        const updatedOptions = { ...variantOptions };
        delete updatedOptions[optionName];
        setVariantOptions(updatedOptions);
        
        setData('variant_options', data.variant_options.filter(opt => opt !== optionName));
    };

    // Update variant option values
    const updateVariantOptionValues = (optionName: string) => {
        const currentValues = variantOptions[optionName] || [];
        const newValues = prompt(`Enter values for ${optionName} (comma-separated):`, currentValues.join(', '));
        if (newValues) {
            const values = newValues.split(',').map(v => v.trim());
            setVariantOptions({
                ...variantOptions,
                [optionName]: values,
            });
        }
    };

    return (
        <AppSidebarLayout>
            <Head title="Create Product" />
            
            <div className="p-6">
                <div className="flex items-center justify-between mb-6">
                    <div>
                    <h1 className="text-2xl font-semibold">Create Product</h1>
                        <p className="text-sm text-muted-foreground">Add a new product to your company</p>
                    </div>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                            <TabsList className="grid w-full grid-cols-3">
                                <TabsTrigger value="basic">Basic Information</TabsTrigger>
                                <TabsTrigger value="details">Additional Details</TabsTrigger>
                            <TabsTrigger value="variants" disabled={!data.is_variant}>Variants</TabsTrigger>
                            </TabsList>
                            
                            <TabsContent value="basic" className="space-y-4">
                                <Card>
                                    <CardHeader>
                                    <CardTitle>Product Information</CardTitle>
                                        <CardDescription>
                                        Enter the product's basic information
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="name">Full Name</Label>
                                            <Input
                                                id="name"
                                                placeholder="Enter product name"
                                                value={data.name}
                                                onChange={e => setData('name', e.target.value)}
                                            />
                                            {errors.name && (
                                                <p className="text-red-500 text-sm mt-1">{errors.name}</p>
                                            )}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="code">Product Code</Label>
                                            <Input
                                                id="code"
                                                placeholder="Enter product code"
                                                value={data.code}
                                                onChange={e => setData('code', e.target.value)}
                                            />
                                            {errors.code && (
                                                <p className="text-red-500 text-sm mt-1">{errors.code}</p>
                                            )}
                                        </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="product_type">Product Type</Label>
                                                        <Select
                                                value={data.product_type}
                                                onValueChange={handleProductTypeChange}
                                                        >
                                                <SelectTrigger id="product_type">
                                                                    <SelectValue placeholder="Select product type" />
                                                                </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="standard">Standard</SelectItem>
                                                                <SelectItem value="combo">Combo</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                            {errors.product_type && (
                                                <p className="text-red-500 text-sm mt-1">{errors.product_type}</p>
                                            )}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="status">Status</Label>
                                                        <Select
                                                value={data.status}
                                                onValueChange={handleStatusChange}
                                                        >
                                                <SelectTrigger id="status">
                                                                    <SelectValue placeholder="Select status" />
                                                                </SelectTrigger>
                                                            <SelectContent>
                                                                <SelectItem value="active">Active</SelectItem>
                                                                <SelectItem value="inactive">Inactive</SelectItem>
                                                            </SelectContent>
                                                        </Select>
                                            {errors.status && (
                                                <p className="text-red-500 text-sm mt-1">{errors.status}</p>
                                                )}
                                        </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="purchase_price">Purchase Price</Label>
                                                            <Input 
                                                id="purchase_price"
                                                                type="number" 
                                                placeholder="Enter purchase price"
                                                value={data.purchase_price}
                                                onChange={e => setData('purchase_price', parseFloat(e.target.value))}
                                                min="0"
                                                step="0.01"
                                            />
                                            {errors.purchase_price && (
                                                <p className="text-red-500 text-sm mt-1">{errors.purchase_price}</p>
                                            )}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="sell_price">Sell Price</Label>
                                                            <Input 
                                                id="sell_price"
                                                                type="number" 
                                                placeholder="Enter sell price"
                                                value={data.sell_price}
                                                onChange={e => setData('sell_price', parseFloat(e.target.value))}
                                                min="0"
                                                step="0.01"
                                            />
                                            {errors.sell_price && (
                                                <p className="text-red-500 text-sm mt-1">{errors.sell_price}</p>
                                            )}
                                        </div>
                                        </div>
                                        
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="negotiation_price">Negotiation Price</Label>
                                                            <Input 
                                                id="negotiation_price"
                                                                type="number" 
                                                placeholder="Enter negotiation price"
                                                value={data.negotiation_price}
                                                onChange={e => setData('negotiation_price', parseFloat(e.target.value))}
                                                min="0"
                                                step="0.01"
                                            />
                                            {errors.negotiation_price && (
                                                <p className="text-red-500 text-sm mt-1">{errors.negotiation_price}</p>
                                            )}
                                        </div>
                                        
                                        <div>
                                            <Label htmlFor="delivery_price">Delivery Price</Label>
                                                            <Input 
                                                id="delivery_price"
                                                                type="number" 
                                                placeholder="Enter delivery price"
                                                value={data.delivery_price}
                                                onChange={e => setData('delivery_price', parseFloat(e.target.value))}
                                                min="0"
                                                step="0.01"
                                            />
                                            {errors.delivery_price && (
                                                <p className="text-red-500 text-sm mt-1">{errors.delivery_price}</p>
                                            )}
                                        </div>
                                        </div>
                                        
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="alert_quantity">Alert Quantity</Label>
                                                        <Input 
                                                id="alert_quantity"
                                                            type="number" 
                                                placeholder="Enter alert quantity"
                                                value={data.alert_quantity}
                                                onChange={e => setData('alert_quantity', parseInt(e.target.value))}
                                                min="0"
                                            />
                                            {errors.alert_quantity && (
                                                <p className="text-red-500 text-sm mt-1">{errors.alert_quantity}</p>
                                            )}
                                                    </div>
                                        
                                        <div>
                                            <Label htmlFor="is_variant">Product Variants</Label>
                                            <div className="flex items-center gap-2">
                                                        <Switch
                                                    id="is_variant"
                                                    checked={Boolean(data.is_variant)}
                                                    onCheckedChange={value => {
                                                        setData('is_variant', value);
                                                        if (!value) {
                                                            setData('variants', []);
                                                            setData('variant_options', []);
                                                        }
                                                    }}
                                                    disabled={data.product_type === 'combo'}
                                                />
                                                {data.product_type === 'combo' && (
                                                    <span className="text-sm text-muted-foreground">
                                                        Variants are not available for combo products
                                                    </span>
                                                )}
                                            </div>
                                            {errors.is_variant && (
                                                <p className="text-red-500 text-sm mt-1">{errors.is_variant}</p>
                                            )}
                                        </div>
                                    </div>
                                    
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <Label htmlFor="image">Product Image</Label>
                                                        <div className="flex items-center gap-4">
                                                            <Input
                                                                type="file"
                                                                accept="image/*"
                                                                onChange={handleImageChange}
                                                                className="hidden"
                                                                id="product-image"
                                                            />
                                                            <Label
                                                                htmlFor="product-image"
                                                                className="cursor-pointer bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md flex items-center gap-2"
                                                            >
                                                                <Upload className="h-4 w-4" />
                                                                Upload Image
                                                            </Label>
                                                            {previewImage && (
                                                                <div className="relative w-20 h-20 rounded-md overflow-hidden">
                                                                    <img
                                                                        src={previewImage}
                                                                        alt="Product preview"
                                                                        className="w-full h-full object-cover"
                                                                    />
                                                                </div>
                                                            )}
                                                        </div>
                                        </div>
                                    </div>

                                    {data.product_type === 'combo' && (
                                        <div className="space-y-4">
                                            <Label>Select Products for Combo</Label>
                                            <MultiSelect
                                                options={products.map(product => ({
                                                    value: product.id,
                                                    label: `${product.name} (${product.code}) - $${parseFloat(String(product.sell_price)).toFixed(2)}`
                                                }))}
                                                placeholder="Search and select products..."
                                                onValueChange={handleProductSelection}
                                                value={data.product_list?.map(String) || []}
                                                maxCount={5}
                                            />
                                            {Array.isArray(data.product_list) && data.product_list.length > 0 && (
                                                <div className="border rounded-lg p-4">
                                                    <h4 className="text-sm font-medium mb-2">Selected Products Summary</h4>
                                                    <div className="space-y-2">
                                                        {data.product_list.map((productId) => {
                                                            const product = products.find(p => p.id === productId);
                                                            return product ? (
                                                                <div key={product.id} className="flex items-center justify-between p-2 bg-muted/50 rounded-md">
                                                                    <div>
                                                                        <p className="font-medium">{product.name}</p>
                                                                        <p className="text-sm text-muted-foreground">
                                                                            Code: {product.code} | Price: ${parseFloat(String(product.sell_price)).toFixed(2)}
                                                                        </p>
                                                                    </div>
                                                                    <Button
                                                                        type="button"
                                                                        variant="ghost"
                                                                        size="sm"
                                                                        onClick={() => {
                                                                            const updatedList = data.product_list.filter(id => id !== product.id);
                                                                            const prices = calculatePrices(updatedList);
                                                                            setData({
                                                                                ...data,
                                                                                product_list: updatedList,
                                                                                ...prices
                                                                            });
                                                                        }}
                                                                    >
                                                                        <Trash2 className="h-4 w-4 text-destructive" />
                                                                    </Button>
                                                                </div>
                                                            ) : null;
                                                        })}
                                                        <div className="mt-4 pt-4 border-t space-y-2">
                                                            <div className="flex justify-between">
                                                                <span className="font-medium">Total Purchase Price:</span>
                                                                <span>${parseFloat(String(data.purchase_price || 0)).toFixed(2)}</span>
                                                            </div>
                                                            <div className="flex justify-between">
                                                                <span className="font-medium">Total Sell Price:</span>
                                                                <span>${parseFloat(String(data.sell_price || 0)).toFixed(2)}</span>
                                                            </div>
                                                            <div className="flex justify-between">
                                                                <span className="font-medium">Negotiation Price:</span>
                                                                <span>${parseFloat(String(data.negotiation_price || 0)).toFixed(2)}</span>
                                                            </div>
                                                            <div className="flex justify-between">
                                                                <span className="font-medium">Delivery Price:</span>
                                                                <span>${parseFloat(String(data.delivery_price || 0)).toFixed(2)}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </TabsContent>
                        
                        <TabsContent value="details" className="space-y-4">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Additional Details</CardTitle>
                                    <CardDescription>
                                        Add additional details and notes for your product.
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div>
                                        <Label htmlFor="product_details">Product Details</Label>
                                        <Textarea
                                            id="product_details"
                                            value={data.product_details}
                                            onChange={e => setData('product_details', e.target.value)}
                                            error={errors.product_details}
                                            placeholder="Enter product details"
                                            className="min-h-[100px]"
                                        />
                                        {errors.product_details && (
                                            <p className="text-red-500 text-sm mt-1">{errors.product_details}</p>
                                        )}
                                    </div>
                                    
                                    <div>
                                        <Label htmlFor="product_notes">Product Notes</Label>
                                        <Textarea
                                            id="product_notes"
                                            value={data.product_notes}
                                            onChange={e => setData('product_notes', e.target.value)}
                                            error={errors.product_notes}
                                            placeholder="Enter product notes"
                                            className="min-h-[100px]"
                                        />
                                        {errors.product_notes && (
                                            <p className="text-red-500 text-sm mt-1">{errors.product_notes}</p>
                                        )}
                                    </div>
                                    </CardContent>
                                </Card>
                            </TabsContent>
                            
                            <TabsContent value="variants" className="space-y-4">
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Product Variants</CardTitle>
                                        <CardDescription>
                                            Define variant options and create variants for your product.
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between">
                                                <h3 className="text-lg font-medium">Variant Options</h3>
                                            <div className="flex gap-2">
                                                <Button 
                                                    type="button" 
                                                    variant="outline" 
                                                    size="sm" 
                                                    onClick={() => {
                                                        setEditingOption(null);
                                                        setNewOptionName('');
                                                        setNewOptionValues('');
                                                        setIsOptionDialogOpen(true);
                                                    }}
                                                >
                                                    <Plus className="h-4 w-4 mr-2" />
                                                    Add Option
                                                </Button>
                                                {Object.keys(variantOptions).length > 0 && (
                                                    <Button
                                                        type="button"
                                                        variant="secondary"
                                                        size="sm"
                                                        onClick={generateAllCombinations}
                                                    >
                                                        Generate All Combinations
                                                    </Button>
                                                )}
                                            </div>
                                            </div>
                                        
                                        <Dialog open={isOptionDialogOpen} onOpenChange={setIsOptionDialogOpen}>
                                            <DialogContent>
                                                <DialogHeader>
                                                    <DialogTitle>
                                                        {editingOption ? 'Edit Variant Option' : 'Add Variant Option'}
                                                    </DialogTitle>
                                                    <DialogDescription>
                                                        {editingOption ? 'Edit the option values' : 'Add a new variant option like Color, Size, etc.'}
                                                    </DialogDescription>
                                                </DialogHeader>
                                                <div className="space-y-4">
                                                    {!editingOption && (
                                                        <div className="space-y-2">
                                                            <Label htmlFor="optionName">Option Name</Label>
                                                            <Input
                                                                id="optionName"
                                                                value={newOptionName}
                                                                onChange={(e) => setNewOptionName(e.target.value)}
                                                                placeholder="e.g., Color, Size"
                                                            />
                                                        </div>
                                                    )}
                                                    <div className="space-y-2">
                                                        <Label htmlFor="optionValues">Option Values (comma-separated)</Label>
                                                        <Input
                                                            id="optionValues"
                                                            value={newOptionValues}
                                                            onChange={(e) => setNewOptionValues(e.target.value)}
                                                            placeholder="e.g., Red, Green, Blue"
                                                        />
                                                    </div>
                                                </div>
                                                <DialogFooter>
                                                    <Button
                                                        type="button"
                                                        variant="outline"
                                                        onClick={() => setIsOptionDialogOpen(false)}
                                                    >
                                                        Cancel
                                                    </Button>
                                                    <Button
                                                        type="button"
                                                        onClick={addVariantOption}
                                                    >
                                                        {editingOption ? 'Update' : 'Add'}
                                                    </Button>
                                                </DialogFooter>
                                            </DialogContent>
                                        </Dialog>
                                            
                                            {Object.keys(variantOptions).length === 0 ? (
                                                <div className="text-center py-4 text-muted-foreground">
                                                    No variant options added yet. Add options like Color, Size, etc.
                                                </div>
                                            ) : (
                                                <div className="space-y-2">
                                                    {Object.entries(variantOptions).map(([option, values]) => (
                                                        <div key={option} className="flex items-center justify-between p-2 border rounded-md">
                                                            <div>
                                                                <div className="font-medium">{option}</div>
                                                                <div className="text-sm text-muted-foreground">
                                                                    Values: {values.join(', ')}
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center gap-2">
                                                                <Button
                                                                    type="button"
                                                                    variant="outline"
                                                                    size="sm"
                                                                onClick={() => {
                                                                    setEditingOption(option);
                                                                    setNewOptionValues(values.join(', '));
                                                                    setIsOptionDialogOpen(true);
                                                                }}
                                                                >
                                                                    Edit Values
                                                                </Button>
                                                                <Button
                                                                    type="button"
                                                                    variant="outline"
                                                                    size="sm"
                                                                    onClick={() => removeVariantOption(option)}
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                        
                                        <Separator />
                                        
                                        <div className="space-y-4">
                                            <div className="flex items-center justify-between">
                                                <h3 className="text-lg font-medium">Variants</h3>
                                                <Button 
                                                    type="button" 
                                                    variant="outline" 
                                                    size="sm" 
                                                    onClick={addVariant}
                                                disabled={Object.keys(variantOptions).length === 0 || areAllCombinationsTaken()}
                                                >
                                                    <Plus className="h-4 w-4 mr-2" />
                                                    Add Variant
                                                </Button>
                                            </div>
                                            
                                            {variants.length === 0 ? (
                                                <div className="text-center py-4 text-muted-foreground">
                                                    No variants added yet. Add variant options first, then create variants.
                                                </div>
                                            ) : (
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                    {variants.map((variant, index) => (
                                                    <Card key={index} className="flex flex-col">
                                                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                                                            <CardTitle className="text-sm font-medium">
                                                                Variant {index + 1}
                                                            </CardTitle>
                                                                <Button
                                                                    type="button"
                                                                variant="ghost"
                                                                    size="sm"
                                                                    onClick={() => removeVariant(index)}
                                                                >
                                                                    <Trash2 className="h-4 w-4" />
                                                                </Button>
                                                        </CardHeader>
                                                        <CardContent className="space-y-4">
                                                            <div className="space-y-2">
                                                                    <Label>SKU</Label>
                                                                    <Input
                                                                        value={variant.sku}
                                                                        onChange={(e) => updateVariant(index, 'sku', e.target.value)}
                                                                        placeholder="Enter SKU"
                                                                    />
                                                                </div>
                                                                
                                                            {/* Show option selectors for manually added variants */}
                                                                {Object.entries(variantOptions).map(([option, values]) => (
                                                                <div key={option} className="space-y-2">
                                                                        <Label>{option}</Label>
                                                                        <Select
                                                                            value={variant.variant_combination[option] || ''}
                                                                        onValueChange={(value) => {
                                                                            const newCombination = {
                                                                                ...variant.variant_combination,
                                                                                [option]: value
                                                                            };
                                                                            updateVariant(index, 'variant_combination', newCombination);
                                                                            // Auto-update SKU when combination changes
                                                                            updateVariant(index, 'sku', generateSkuFromCombination(newCombination));
                                                                        }}
                                                                    >
                                                                        <SelectTrigger className="w-full">
                                                                            <SelectValue placeholder={`Select ${option}`}>
                                                                                {variant.variant_combination[option] || `Select ${option}`}
                                                                            </SelectValue>
                                                                            </SelectTrigger>
                                                                            <SelectContent>
                                                                                {values.map((value) => (
                                                                                    <SelectItem key={value} value={value}>
                                                                                        {value}
                                                                                    </SelectItem>
                                                                                ))}
                                                                            </SelectContent>
                                                                        </Select>
                                                                    </div>
                                                                ))}
                                                            
                                                            <div className="grid grid-cols-2 gap-2">
                                                                <div>
                                                                    <Label>Additional Price</Label>
                                                                    <Input
                                                                        type="number"
                                                                        value={variant.additional_sell_price}
                                                                        onChange={(e) => updateVariant(index, 'additional_sell_price', parseFloat(e.target.value))}
                                                                    />
                                                                </div>
                                                                <div>
                                                                    <Label>Quantity</Label>
                                                                    <Input
                                                                        type="number"
                                                                        value={variant.total_qty}
                                                                        onChange={(e) => updateVariant(index, 'total_qty', parseFloat(e.target.value))}
                                                                    />
                                                                </div>
                                                                </div>
                                                                
                                                                <div>
                                                                    <Label>Status</Label>
                                                                    <Select
                                                                        value={variant.status}
                                                                    onValueChange={(value) => updateVariant(index, 'status', value as 'active' | 'inactive')}
                                                                    >
                                                                        <SelectTrigger>
                                                                        <SelectValue>
                                                                            {variant.status.charAt(0).toUpperCase() + variant.status.slice(1)}
                                                                        </SelectValue>
                                                                        </SelectTrigger>
                                                                        <SelectContent>
                                                                            <SelectItem value="active">Active</SelectItem>
                                                                            <SelectItem value="inactive">Inactive</SelectItem>
                                                                        </SelectContent>
                                                                    </Select>
                                                            </div>
                                                            
                                                            <div className="space-y-2">
                                                                <Label>Variant Image</Label>
                                                                <div className="flex items-center gap-4">
                                                                    <Input
                                                                        type="file"
                                                                        accept="image/*"
                                                                        onChange={(e) => handleVariantImageChange(index, e)}
                                                                        className="hidden"
                                                                        id={`variant-image-${index}`}
                                                                    />
                                                                    <Label
                                                                        htmlFor={`variant-image-${index}`}
                                                                        className="cursor-pointer bg-muted hover:bg-muted/80 text-muted-foreground px-4 py-2 rounded-md flex items-center gap-2"
                                                                    >
                                                                        <Upload className="h-4 w-4" />
                                                                        Upload Image
                                                                    </Label>
                                                                    {variantPreviewImages[index] && (
                                                                        <div className="relative w-20 h-20 rounded-md overflow-hidden">
                                                                            <img
                                                                                src={variantPreviewImages[index]}
                                                                                alt={`Variant ${index + 1} preview`}
                                                                                className="w-full h-full object-cover"
                                                                            />
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                        </Card>
                                                    ))}
                                                </div>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>
                            </TabsContent>
                        </Tabs>
                        
                        <div className="flex justify-end gap-4">
                            <Button type="button" variant="outline" onClick={() => window.history.back()}>
                                Cancel
                            </Button>
                            <Button type="submit" disabled={processing}>
                                {processing ? 'Creating...' : 'Create Product'}
                            </Button>
                        </div>
                    </form>
            </div>
        </AppSidebarLayout>
    );
} 