# Quick YouCan Testing Script - Run from project root

Write-Host "🚀 YouCan Quick Test" -ForegroundColor Green
Write-Host "===================" -ForegroundColor Green
Write-Host "Current directory: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

# Check if artisan file exists
if (-not (Test-Path "artisan")) {
    Write-Host "❌ Error: artisan file not found!" -ForegroundColor Red
    Write-Host "Please run this script from the Laravel project root directory." -ForegroundColor Red
    Write-Host "Expected files: artisan, composer.json, .env" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host "✅ Found artisan file - we're in the right directory" -ForegroundColor Green
Write-Host ""

function Show-Menu {
    Write-Host "Choose a test:" -ForegroundColor Cyan
    Write-Host "1) Test YouCan API connection"
    Write-Host "2) Import 200 orders from YouCan"
    Write-Host "3) Check import progress"
    Write-Host "4) Test webhook endpoint"
    Write-Host "5) Show recent logs"
    Write-Host "0) Exit"
    Write-Host ""
}

function Test-YouCanConnection {
    Write-Host "ℹ️  Testing YouCan API connection..." -ForegroundColor Blue
    php artisan youcan:test-import --company-id=1 --test-connection
}

function Import-Orders {
    Write-Host "ℹ️  Importing 200 orders from YouCan..." -ForegroundColor Blue
    Write-Host "This will dispatch a background job." -ForegroundColor Yellow
    Write-Host "Make sure your queue worker is running: php artisan queue:work" -ForegroundColor Yellow
    Write-Host ""

    $phpCode = "require 'vendor/autoload.php'; `$app = require_once 'bootstrap/app.php'; `$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); App\Jobs\SyncYoucanOrders::dispatch(1, ['limit' => 200, 'page' => 1]); echo '✅ Import job dispatched!' . PHP_EOL;"

    php -r $phpCode
}

function Check-Progress {
    Write-Host "ℹ️  Checking import progress..." -ForegroundColor Blue

    $phpCode = "require 'vendor/autoload.php'; `$app = require_once 'bootstrap/app.php'; `$app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); `$count = App\Models\Order::where('source', 'youcan')->count(); `$customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count(); `$latest = App\Models\Order::where('source', 'youcan')->latest()->first(); echo '📊 Import Statistics:' . PHP_EOL; echo '  - Orders imported: ' . `$count . PHP_EOL; echo '  - Customers created: ' . `$customerCount . PHP_EOL; echo '  - Latest order: ' . (`$latest ? `$latest->ref . ' (' . `$latest->created_at . ')' : 'None') . PHP_EOL;"

    php -r $phpCode
}

function Test-Webhook {
    Write-Host "ℹ️  Testing webhook endpoint..." -ForegroundColor Blue
    Write-Host "Make sure ngrok is running and update your .env with the ngrok URL" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "To test webhook manually, run:"
    Write-Host 'curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \'
    Write-Host '     -H "Content-Type: application/json" \'
    Write-Host '     -d "{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}"'
    Write-Host ""
    Write-Host "Or subscribe to webhooks via your web interface at:"
    Write-Host "http://localhost:8000/youcan/import"
}

function Show-Logs {
    Write-Host "ℹ️  Showing recent YouCan-related logs..." -ForegroundColor Blue
    
    if (Test-Path "storage/logs/laravel.log") {
        Get-Content "storage/logs/laravel.log" -Tail 20 | Select-String -Pattern "youcan|order|webhook" -CaseSensitive:$false
    }
    else {
        Write-Host "❌ Log file not found: storage/logs/laravel.log" -ForegroundColor Red
    }
}

# Main script loop
do {
    Show-Menu
    $choice = Read-Host "Enter your choice [0-5]"
    
    switch ($choice) {
        "1" {
            Test-YouCanConnection
        }
        "2" {
            Import-Orders
        }
        "3" {
            Check-Progress
        }
        "4" {
            Test-Webhook
        }
        "5" {
            Show-Logs
        }
        "0" {
            Write-Host "✅ Goodbye!" -ForegroundColor Green
            exit
        }
        default {
            Write-Host "❌ Invalid choice. Please try again." -ForegroundColor Red
        }
    }
    
    Write-Host ""
    Read-Host "Press Enter to continue"
    Clear-Host
    Write-Host "🚀 YouCan Quick Test" -ForegroundColor Green
    Write-Host "===================" -ForegroundColor Green
    Write-Host ""
    
} while ($true)
