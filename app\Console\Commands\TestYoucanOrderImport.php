<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Jobs\SyncYoucanOrders;
use App\Models\Company;
use App\Models\CompanyCmsIntegration;
use App\Models\CmsPlatform;
use App\Models\Order;
use App\Services\GeographicMappingService;

class TestYoucanOrderImport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'youcan:test-import
                            {--company-id= : Company ID to test with}
                            {--test-geo : Test geographic mapping only}
                            {--clear-cache : Clear geographic cache}
                            {--check-dates : Check existing orders date range}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test YouCan order import functionality with intelligent duplicate detection and geographic mapping';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Add option to check existing orders date range
        if ($this->option('check-dates')) {
            return $this->checkExistingOrderDates();
        }
        $this->info('🚀 Testing YouCan Order Import System');
        $this->newLine();

        // Clear cache if requested
        if ($this->option('clear-cache')) {
            $geoService = new GeographicMappingService();
            $geoService->clearCache();
            $this->info('✅ Geographic cache cleared');
            $this->newLine();
        }

        // Test geographic mapping if requested
        if ($this->option('test-geo')) {
            $this->testGeographicMapping();
            $this->testPhoneParsing();
            return;
        }

        // Get company for testing
        $companyId = $this->option('company-id');
        if (!$companyId) {
            $companies = Company::with('cmsIntegrations.cmsPlatform')
                ->whereHas('cmsIntegrations.cmsPlatform', function($query) {
                    $query->where('slug', 'youcan');
                })
                ->get();

            if ($companies->isEmpty()) {
                $this->error('❌ No companies found with YouCan integration');
                return;
            }

            $this->table(
                ['ID', 'Name', 'Domain', 'YouCan Integration'],
                $companies->map(function($company) {
                    $integration = $company->cmsIntegrations->where('cmsPlatform.slug', 'youcan')->first();
                    return [
                        $company->id,
                        $company->name,
                        $company->domain,
                        $integration ? ($integration->is_active ? '✅ Active' : '⚠️ Inactive') : '❌ None'
                    ];
                })
            );

            $companyId = $this->ask('Enter Company ID to test with');
        }

        $company = Company::find($companyId);
        if (!$company) {
            $this->error("❌ Company with ID {$companyId} not found");
            return;
        }

        $this->info("🏢 Testing with company: {$company->name}");
        $this->newLine();

        // Check YouCan integration
        $integration = $this->checkYoucanIntegration($company);
        if (!$integration) {
            return;
        }

        // Test order import
        $this->testOrderImport($company);
    }

    /**
     * Check existing orders date range
     */
    protected function checkExistingOrderDates()
    {
        $this->info('📊 Checking existing YouCan orders date range...');
        $this->newLine();

        $oldest = Order::where('source', 'youcan')->oldest()->first();
        $newest = Order::where('source', 'youcan')->latest()->first();
        $total = Order::where('source', 'youcan')->count();

        if (!$oldest) {
            $this->warn('❌ No YouCan orders found in database');
            return;
        }

        $this->info("📈 YouCan Orders Analysis:");
        $this->line("   Total orders: {$total}");
        $this->line("   Oldest order: {$oldest->created_at->format('Y-m-d H:i:s')} (ref: {$oldest->ref})");
        $this->line("   Newest order: {$newest->created_at->format('Y-m-d H:i:s')} (ref: {$newest->ref})");
        $this->line("   Date span: {$oldest->created_at->diffInDays($newest->created_at)} days");

        $this->newLine();
        $this->info("💡 Suggestions for importing missing orders:");
        $this->line("   - Import older orders: before {$oldest->created_at->format('Y-m-d')}");
        $this->line("   - Import newer orders: after {$newest->created_at->format('Y-m-d')}");
        $this->newLine();

        return;
    }

    /**
     * Test geographic mapping functionality
     */
    protected function testGeographicMapping()
    {
        $this->info('🌍 Testing Geographic Mapping');
        $this->newLine();

        $geoService = new GeographicMappingService();

        // Test cases based on YouCan order structure
        $testCases = [
            [
                'name' => 'Morocco - Fez',
                'data' => [
                    'country_code' => 'MA',
                    'country_name' => 'Morocco',
                    'state' => null,
                    'city' => 'Fez'
                ]
            ],
            [
                'name' => 'USA - California - Los Angeles',
                'data' => [
                    'country_code' => 'US',
                    'country_name' => 'United States',
                    'state' => 'California',
                    'city' => 'Los Angeles'
                ]
            ],
            [
                'name' => 'Tunisia - Tunis',
                'data' => [
                    'country_code' => 'TN',
                    'country_name' => 'Tunisia',
                    'state' => 'Tunis',
                    'city' => 'Tunis'
                ]
            ],
            [
                'name' => 'Unknown Country',
                'data' => [
                    'country_code' => 'XX',
                    'country_name' => 'Unknown Country',
                    'state' => 'Unknown State',
                    'city' => 'Unknown City'
                ]
            ]
        ];

        foreach ($testCases as $testCase) {
            $this->info("Testing: {$testCase['name']}");
            
            $result = $geoService->validateAndSuggest($testCase['data']);
            
            $this->line("Input: " . json_encode($testCase['data']));
            $this->line("Resolved: " . json_encode($result['resolved']));
            
            if (!empty($result['suggestions'])) {
                $this->warn("Suggestions: " . json_encode($result['suggestions']));
            }
            
            $this->newLine();
        }
    }

    /**
     * Test phone parsing functionality
     */
    protected function testPhoneParsing()
    {
        $this->info('📞 Testing Phone Number Parsing');
        $this->newLine();

        // Create a temporary job instance to access the phone parsing method
        $job = new \App\Jobs\SyncYoucanOrders(1, []);
        $reflection = new \ReflectionClass($job);
        $parseMethod = $reflection->getMethod('parsePhoneNumber');
        $parseMethod->setAccessible(true);

        // Test cases for phone parsing
        $testCases = [
            [
                'name' => 'Morocco phone with +212',
                'phone' => '+212676461601',
                'country' => 'MA',
                'expected' => ['phone' => '676461601', 'phone_code' => '212']
            ],
            [
                'name' => 'Tunisia phone with +216',
                'phone' => '+216123456789',
                'country' => 'TN',
                'expected' => ['phone' => '123456789', 'phone_code' => '216']
            ],
            [
                'name' => 'US phone with +1',
                'phone' => '+1234567890',
                'country' => 'US',
                'expected' => ['phone' => '234567890', 'phone_code' => '1']
            ],
            [
                'name' => 'Phone without + but with country',
                'phone' => '676461601',
                'country' => 'MA',
                'expected' => ['phone' => '676461601', 'phone_code' => '212']
            ],
            [
                'name' => 'Phone with country code prefix',
                'phone' => '212676461601',
                'country' => 'MA',
                'expected' => ['phone' => '676461601', 'phone_code' => '212']
            ],
            [
                'name' => 'Phone with spaces and dashes',
                'phone' => '+212 67-64-61-601',
                'country' => 'MA',
                'expected' => ['phone' => '676461601', 'phone_code' => '212']
            ],
            [
                'name' => 'Invalid phone',
                'phone' => '',
                'country' => 'MA',
                'expected' => ['phone' => null, 'phone_code' => null]
            ]
        ];

        foreach ($testCases as $testCase) {
            $this->info("Testing: {$testCase['name']}");

            $result = $parseMethod->invoke($job, $testCase['phone'], $testCase['country']);

            $this->line("Input: {$testCase['phone']} (Country: {$testCase['country']})");
            $this->line("Expected: " . json_encode($testCase['expected']));
            $this->line("Result: " . json_encode($result));

            if ($result == $testCase['expected']) {
                $this->info("✅ PASS");
            } else {
                $this->error("❌ FAIL");
            }

            $this->newLine();
        }
    }

    /**
     * Check YouCan integration for company
     */
    protected function checkYoucanIntegration(Company $company): ?CompanyCmsIntegration
    {
        $youcanPlatform = CmsPlatform::where('slug', 'youcan')->first();
        if (!$youcanPlatform) {
            $this->error('❌ YouCan platform not found in database');
            return null;
        }

        $integration = CompanyCmsIntegration::where('company_id', $company->id)
            ->where('cms_platform_id', $youcanPlatform->id)
            ->first();

        if (!$integration) {
            $this->error('❌ No YouCan integration found for this company');
            $this->line('💡 Please configure YouCan integration in the admin panel first');
            return null;
        }

        if (!$integration->is_active) {
            $this->warn('⚠️ YouCan integration is inactive');
            if (!$this->confirm('Continue anyway?')) {
                return null;
            }
        }

        $this->info('✅ YouCan integration found and active');
        $this->newLine();

        return $integration;
    }

    /**
     * Test order import functionality
     */
    protected function testOrderImport(Company $company)
    {
        $this->info('📦 Testing Order Import');
        $this->newLine();

        // Ask for import parameters
        $params = [];
        
        if ($this->confirm('Import orders from a specific date range?')) {
            $params['created_at_min'] = $this->ask('Start date (YYYY-MM-DD)', date('Y-m-d', strtotime('-7 days')));
            $params['created_at_max'] = $this->ask('End date (YYYY-MM-DD)', date('Y-m-d'));
        }

        $params['limit'] = $this->ask('Number of orders to import', '5');

        $this->info('🚀 Dispatching order import job...');
        $this->line('Parameters: ' . json_encode($params));
        $this->newLine();

        try {
            // Dispatch the job
            SyncYoucanOrders::dispatch($company->id, $params);
            
            $this->info('✅ Order import job dispatched successfully!');
            $this->line('💡 Check the logs for detailed import progress');
            $this->line('💡 Monitor the orders table for imported orders');
            
            $this->newLine();
            $this->info('📊 To monitor the import:');
            $this->line('- Check Laravel logs: tail -f storage/logs/laravel.log');
            $this->line('- Check queue jobs: php artisan queue:work');
            $this->line('- Check orders in database or admin panel');
            
        } catch (\Exception $e) {
            $this->error('❌ Failed to dispatch order import job');
            $this->error('Error: ' . $e->getMessage());
        }
    }
}
