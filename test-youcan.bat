@echo off
REM Quick YouCan Testing Script - Run from project root
echo.
echo 🚀 YouCan Quick Test
echo ===================
echo Current directory: %CD%
echo.

REM Check if artisan file exists
if not exist "artisan" (
    echo ❌ Error: artisan file not found!
    echo Please run this script from the Laravel project root directory.
    echo Expected files: artisan, composer.json, .env
    pause
    exit /b 1
)

echo ✅ Found artisan file - we're in the right directory
echo.

:menu
echo Choose a test:
echo 1) Test YouCan API connection
echo 2) Import 200 orders from YouCan
echo 3) Check import progress
echo 4) Test webhook endpoint
echo 5) Show recent logs
echo 0) Exit
echo.
set /p choice="Enter your choice [0-5]: "

if "%choice%"=="1" goto test_connection
if "%choice%"=="2" goto import_orders
if "%choice%"=="3" goto check_progress
if "%choice%"=="4" goto test_webhook
if "%choice%"=="5" goto show_logs
if "%choice%"=="0" goto exit
echo Invalid choice. Please try again.
goto menu

:test_connection
echo.
echo ℹ️  Testing YouCan API connection...
php artisan youcan:test-import --company-id=1 --test-connection
goto continue

:import_orders
echo.
echo ℹ️  Importing 200 orders from YouCan...
echo This will dispatch a background job.
echo Make sure your queue worker is running: php artisan queue:work
echo.
php -r "require 'vendor/autoload.php'; $app = require_once 'bootstrap/app.php'; $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); App\Jobs\SyncYoucanOrders::dispatch(1, ['limit' => 200, 'page' => 1]); echo '✅ Import job dispatched!' . PHP_EOL;"
goto continue

:check_progress
echo.
echo ℹ️  Checking import progress...
php -r "require 'vendor/autoload.php'; $app = require_once 'bootstrap/app.php'; $app->make(Illuminate\Contracts\Console\Kernel::class)->bootstrap(); $count = App\Models\Order::where('source', 'youcan')->count(); $customerCount = App\Models\Customer::whereNotNull('youcan_customer_id')->count(); $latest = App\Models\Order::where('source', 'youcan')->latest()->first(); echo '📊 Import Statistics:' . PHP_EOL; echo '  - Orders imported: ' . $count . PHP_EOL; echo '  - Customers created: ' . $customerCount . PHP_EOL; echo '  - Latest order: ' . ($latest ? $latest->ref . ' (' . $latest->created_at . ')' : 'None') . PHP_EOL;"
goto continue

:test_webhook
echo.
echo ℹ️  Testing webhook endpoint...
echo Make sure ngrok is running and update your .env with the ngrok URL
echo.
echo To test webhook manually, run:
echo curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \
echo      -H "Content-Type: application/json" \
echo      -d "{\"event\": \"order.create\", \"data\": {\"id\": \"test-123\"}}"
echo.
echo Or subscribe to webhooks via your web interface at:
echo http://localhost:8000/youcan/import
goto continue

:show_logs
echo.
echo ℹ️  Showing recent YouCan-related logs...
if exist "storage\logs\laravel.log" (
    powershell -Command "Get-Content 'storage\logs\laravel.log' -Tail 20 | Select-String -Pattern 'youcan|order|webhook' -CaseSensitive:$false"
) else (
    echo ❌ Log file not found: storage\logs\laravel.log
)
goto continue

:continue
echo.
pause
goto menu

:exit
echo.
echo ✅ Goodbye!
pause
exit /b 0
