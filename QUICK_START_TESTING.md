# 🚀 Quick Start: YouCan Webhook & Order Import Testing

## ⚡ **Immediate Testing Steps**

### **Step 1: Prepare Environment (2 minutes)**

1. **Open 4 terminals/command prompts:**
   - Terminal 1: Laravel server
   - Terminal 2: Queue worker  
   - Terminal 3: ngrok tunnel
   - Terminal 4: Testing script

2. **Start Laravel (Terminal 1):**
   ```bash
   php artisan serve --host=0.0.0.0 --port=8000
   ```

3. **Start Queue Worker (Terminal 2):**
   ```bash
   php artisan queue:work --tries=3 --timeout=300
   ```

4. **Start ngrok (Terminal 3):**
   ```bash
   ngrok http 8000
   # Copy the HTTPS URL (e.g., https://abc123.ngrok.io)
   ```

5. **Update .env file with ngrok URL:**
   ```env
   YOUCAN_NGROK_URL=https://abc123.ngrok.io
   APP_URL=https://abc123.ngrok.io
   ```

### **Step 2: Run Testing Script (Terminal 4)**

**Option A: PowerShell (Recommended for Windows)**
```powershell
cd scripts
.\test-youcan-import.ps1
```

**Option B: Batch File**
```cmd
cd scripts
test-youcan-import.bat
```

**Option C: Manual Commands**
```bash
# Test connection
php artisan youcan:test-import --company-id=1 --test-connection

# Import 200 orders
php artisan tinker
>>> App\Jobs\SyncYoucanOrders::dispatch(1, ['limit' => 200]);
```

### **Step 3: Monitor Progress**

**Check import progress:**
```bash
php artisan tinker
>>> $count = App\Models\Order::where('source', 'youcan')->count();
>>> echo "Orders imported: {$count}";
```

**Monitor logs:**
```bash
tail -f storage/logs/laravel.log | findstr /i "youcan order"
```

## 🎯 **Testing Scenarios**

### **Scenario 1: Bulk Import Test (200 Orders)**
```php
// Via tinker
App\Jobs\SyncYoucanOrders::dispatch(1, [
    'limit' => 200,
    'page' => 1
]);
```

### **Scenario 2: Date Range Import**
```php
// Import last 30 days (max 200)
App\Jobs\SyncYoucanOrders::dispatch(1, [
    'limit' => 200,
    'created_at_min' => now()->subDays(30)->format('Y-m-d'),
    'created_at_max' => now()->format('Y-m-d')
]);
```

### **Scenario 3: Webhook Test**
```bash
# Test webhook endpoint
curl -X POST https://your-ngrok-url.ngrok.io/api/youcan/webhook \
     -H "Content-Type: application/json" \
     -d '{"event": "order.create", "data": {"id": "test-123", "ref": "TEST-001"}}'
```

## 📊 **Quick Verification Commands**

### **Check Import Results:**
```php
// In tinker
$stats = [
    'orders' => App\Models\Order::where('source', 'youcan')->count(),
    'customers' => App\Models\Customer::whereNotNull('youcan_customer_id')->count(),
    'latest' => App\Models\Order::where('source', 'youcan')->latest()->first()?->ref
];
print_r($stats);
```

### **Check Webhook Status:**
```bash
# Via web interface
curl -X GET http://localhost:8000/youcan/webhook-status \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## 🔧 **Quick Troubleshooting**

### **Issue: Orders not importing**
```bash
# Check API connection
php artisan youcan:test-import --company-id=1 --test-connection

# Check integration
php artisan tinker
>>> App\Models\CompanyCmsIntegration::where('company_id', 1)->first()
```

### **Issue: Queue jobs not processing**
```bash
# Check failed jobs
php artisan queue:failed

# Restart queue worker
# Ctrl+C in queue terminal, then restart:
php artisan queue:work --tries=3 --timeout=300
```

### **Issue: Webhook not receiving**
```bash
# Check ngrok dashboard: http://127.0.0.1:4040
# Verify webhook route
php artisan route:list | findstr webhook
```

## ✅ **Success Checklist**

- [ ] Laravel running on port 8000
- [ ] Queue worker processing jobs
- [ ] ngrok tunnel active with HTTPS URL
- [ ] YouCan API connection successful
- [ ] 200 orders import job dispatched
- [ ] Orders appearing in database
- [ ] Customers being created/matched
- [ ] Webhook endpoint accessible
- [ ] No errors in logs

## 📈 **Expected Results**

After successful testing, you should see:

1. **Orders Table:**
   - ~200 new orders with `source = 'youcan'`
   - `youcan_order_id` populated
   - `youcan_synced_at` timestamp

2. **Customers Table:**
   - New customers with `youcan_customer_id`
   - Phone numbers parsed correctly
   - Geographic data resolved

3. **Logs:**
   - Successful API calls
   - Order processing messages
   - No error messages

## 🚀 **Next Steps After Testing**

1. **Production Webhook Setup:**
   - Replace ngrok URL with production domain
   - Configure proper webhook secrets
   - Set up monitoring and alerts

2. **Optimization:**
   - Adjust chunk sizes based on performance
   - Configure retry policies
   - Set up scheduled imports

3. **Monitoring:**
   - Set up log monitoring
   - Configure error notifications
   - Create import dashboards

---

**🎯 Total Testing Time: ~10-15 minutes**

This quick start guide will get you testing YouCan webhook and order import functionality immediately! 🚀
