<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\StateController;
use App\Http\Controllers\Api\CityController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ProductVariantController;
use App\Http\Controllers\Api\SupplierController;
use App\Http\Controllers\Api\WarehouseController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\YoucanWebhookController;
use App\Http\Controllers\ShopifyWebhookController;

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// States API
Route::get('/states/{country}', [StateController::class, 'index']);

// Cities API
Route::get('/cities/{state}', [CityController::class, 'index']);

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware(['auth:sanctum'])->group(function () {
    // Products
    Route::apiResource('products', ProductController::class);
    Route::apiResource('products.variants', ProductVariantController::class);

    // Warehouses
    Route::apiResource('warehouses', WarehouseController::class);

    // Suppliers
    Route::apiResource('suppliers', SupplierController::class);

    Route::get('/orders/{order}/confirmation-details', [OrderController::class, 'getConfirmationDetails']);
    Route::post('/orders/{order}/call-attempt', [OrderController::class, 'addCallAttempt']);
});

// Test endpoint for ngrok verification
Route::get('test', function () {
    return response()->json([
        'status' => 'success',
        'message' => 'ngrok is working correctly',
        'timestamp' => now(),
        'url' => request()->url(),
        'host' => request()->getHost(),
        'scheme' => request()->getScheme(),
        'is_secure' => request()->isSecure(),
        'app_url' => config('app.url'),
        'force_https' => env('FORCE_HTTPS'),
        'headers' => [
            'x-forwarded-proto' => request()->header('x-forwarded-proto'),
            'x-forwarded-host' => request()->header('x-forwarded-host'),
            'x-forwarded-ssl' => request()->header('x-forwarded-ssl'),
            'https' => request()->server('HTTPS'),
        ],
        'server_vars' => [
            'SERVER_PORT' => request()->server('SERVER_PORT'),
            'REQUEST_SCHEME' => request()->server('REQUEST_SCHEME'),
        ]
    ]);
});

// YouCan Webhook Routes
Route::prefix('youcan')->group(function () {
    // Public webhook endpoint for YouCan to send events
    Route::post('webhook', [YoucanWebhookController::class, 'handle']);

    // Protected routes that require authentication
    Route::middleware(['auth:sanctum'])->group(function () {
        Route::get('webhook/status', [YoucanWebhookController::class, 'status']);
        Route::post('webhook/subscribe', [YoucanWebhookController::class, 'subscribe']);
        Route::post('webhook/unsubscribe', [YoucanWebhookController::class, 'unsubscribe']);
    });
});

// Shopify Webhook Routes
Route::prefix('webhooks/shopify')->group(function () {
    // Public webhook endpoint for Shopify to send events
    Route::post('/', [ShopifyWebhookController::class, 'handle']);
    Route::get('/verify', [ShopifyWebhookController::class, 'verify']);
    Route::post('/test', [ShopifyWebhookController::class, 'test']);
    Route::get('/test', [ShopifyWebhookController::class, 'test']);
});