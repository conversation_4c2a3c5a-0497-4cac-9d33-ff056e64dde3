import { Head } from '@inertiajs/react';
import AppSidebarLayout from '@/layouts/app/app-sidebar-layout';
import AppSettingsExample from '@/components/examples/AppSettingsExample';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Test App Settings',
        href: '/test-app-settings',
    },
];

export default function TestAppSettings() {
    return (
        <AppSidebarLayout breadcrumbs={breadcrumbs}>
            <Head title="Test App Settings" />
            
            <div className="space-y-6">
                <div>
                    <h1 className="text-2xl font-bold">App Settings Test Page</h1>
                    <p className="text-gray-600">
                        This page demonstrates how app settings are used throughout the application.
                    </p>
                </div>
                
                <AppSettingsExample />
            </div>
        </AppSidebarLayout>
    );
}
