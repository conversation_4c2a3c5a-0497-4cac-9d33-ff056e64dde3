# Products Module

## Overview
The Products module manages the company's product catalog, including product information, pricing, inventory, and categorization.

## Features
- Product catalog management
- Product categorization
- Inventory tracking
- Product search and filtering
- Product variants support

## Database Structure

### Products Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Product name |
| sku | string | Stock keeping unit (unique) |
| description | text | Product description |
| price | decimal | Product price |
| cost | decimal | Product cost |
| stock_quantity | integer | Current stock quantity |
| virtual_stock | integer | Virtual/available stock |
| category_id | bigint | Foreign key to categories table |
| supplier_id | bigint | Foreign key to suppliers table |
| is_active | boolean | Whether product is active |
| image_url | string | Product image URL |
| weight | decimal | Product weight |
| dimensions | string | Product dimensions |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Categories Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| name | string | Category name |
| description | text | Category description |
| parent_id | bigint | Self-referencing foreign key for hierarchy |
| company_id | bigint | Foreign key to companies table |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

### Product_Variants Table
| Column | Type | Description |
|--------|------|-------------|
| id | bigint | Primary key |
| product_id | bigint | Foreign key to products table |
| name | string | Variant name |
| sku | string | Variant SKU |
| price | decimal | Variant price |
| stock_quantity | integer | Variant stock quantity |
| attributes | json | Variant attributes (color, size, etc.) |
| created_at | timestamp | Record creation timestamp |
| updated_at | timestamp | Record update timestamp |

## User Interface
- Product listing page with search and filtering
- Product creation and editing forms
- Product details view
- Category management interface
- Inventory management dashboard

## Business Rules
- Products must have unique SKUs within a company
- Stock quantities cannot be negative
- Price and cost must be non-negative values
- Products can be categorized in a hierarchical structure
- Product variants share the base product information but have unique attributes